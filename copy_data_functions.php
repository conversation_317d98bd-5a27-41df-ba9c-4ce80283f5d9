<?php

declare(strict_types=1);

function remote_execute($command) 
{
    return \shell_exec("ssh -p 12488 myproabr@5.132.190.184 \"$command\"");
}

function get_import_statement($columns)
{
    $columnArr = [];
    $optionsArr = [];
    foreach ($columns as $column => $options) {
        if (\in_array(NULLABLE, $options, true)) {
            $columnArr[] = "@$column";
            $optionsArr[] = "$column = nullif(@$column, '')";
        }
        else {
            $columnArr[] = $column;
        }
    }
    $returnString = '(' . implode(', ', $columnArr) . ')';
    $returnString .= empty($optionsArr) ? '' : ' SET ' . implode(', ', $optionsArr);
    return $returnString;
}

function get_update_statement($columns) 
{
    $updateArr = [];
    foreach ($columns as $column => $options) {
        if (empty(array_intersect([PRIMARY, UNIQUE_KEY], $options))) {
            $updateArr[] = "$column = VALUES($column)";
        }
    }

    if (!$updateArr) {
        $fallbackColumn = array_keys($columns)[0]; // if all columns are primary or unique keys, just take the first one for update statement
        $updateArr[] = "$fallbackColumn = VALUES($fallbackColumn)";
    }

    return 'ON DUPLICATE KEY UPDATE ' . \implode(', ', $updateArr);
}

function get_columns_array(string $result, array $exclude = [], array $uniqueColumns = []): array
{
    $result = preg_replace('/\n$/', '', $result); // remove last line to prevent 0-field array
    $rows = explode("\n", $result);
    unset($rows[0]); // remove header row
    $array = [];
    foreach ($rows as $row) {
        $fields = explode("\t", $row);
        
        if (in_array($fields[0], $exclude)) {
            continue;
        }

        $array[$fields[0]] = [];
        if ($fields[2] === 'PRI') {
            $array[$fields[0]][] = PRIMARY;
        }
        if (in_array($fields[0], $uniqueColumns)) {
            $array[$fields[0]][] = UNIQUE_KEY;
        }
        if ($fields[1] === 'YES') {
            $array[$fields[0]][] = NULLABLE;
        }
    }
    return $array;
}

function get_columns_string(array $columns, array $mapping = []): string
{
    $columnsArr = [];
    foreach ($columns as $column => $options) {
        if (in_array($column, array_keys($mapping))) {
            $columnsArr[] = $mapping[$column];
        }
        elseif (in_array(NULLABLE, $options)) {
            $columnsArr[] = 'ifnull(a.'.$column.', \\\"\\\") as '.$column;
        }
        else {
            $columnsArr[] = 'a.'.$column;
        }
    }
    return implode(', ', $columnsArr);
}
