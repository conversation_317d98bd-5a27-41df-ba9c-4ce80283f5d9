# set node version
. ~/.nvm/nvm.sh
nvm use

build() {
    yarn --cwd vendor/creativestyle/theme-ssnoe build
    yarn --cwd vendor/creativestyle/theme-vinotaria build
    yarn --cwd vendor/creativestyle/theme-asm build
    yarn --cwd vendor/creativestyle/theme-manner build
    yarn --cwd vendor/creativestyle/theme-dro<PERSON>ker build
    yarn --cwd vendor/creativestyle/theme-pago build
    yarn --cwd vendor/creativestyle/theme-davidfussenegger build
}
rm -rf app/design/frontend/creativestyle/*
yarn --cwd vendor/creativestyle/theme-creativeshop install
yarn --cwd vendor/creativestyle/theme-creativeshop build
yarn --cwd vendor/creativestyle/theme-myproduct build
build &
wait
echo "All themes built"
