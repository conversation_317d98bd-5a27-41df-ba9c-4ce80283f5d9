build() {
    (cd vendor/creativestyle/theme-erdbeerwoche; npm update)
    (cd vendor/creativestyle/theme-ssnoe; npm update)
    (cd vendor/creativestyle/theme-vinotaria; npm update)
    (cd vendor/creativestyle/theme-asm; npm update)
    (cd vendor/creativestyle/theme-manner; npm update)
    (cd vendor/creativestyle/theme-dro<PERSON>ker; npm update)
    (cd vendor/creativestyle/theme-pago; npm update)
    (cd vendor/creativestyle/theme-davidfussenegger; npm update)
}

(cd vendor/creativestyle/theme-creativeshop; npm update)
(cd vendor/creativestyle/theme-myproduct; npm update)
build &
wait
echo "All theme dependencies updated"
