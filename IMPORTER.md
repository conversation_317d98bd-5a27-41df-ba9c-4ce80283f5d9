# MyProduct Data Import

## Scripts
- copy_data_from_old_magento.php (execute import)
- copy_data_functions.php (functions needed for import)
- copy_data_tables.php (array of tables that are imported)
- copy_data_tables_info.php (compare table from old and new database)

## Execute
All files are located under `~/www.myproduct.at`.  

General
```
Usage: php -f copy_data_from_old_magento.php <old_db_name> <new_db_name> <optional: tables (comma separated)>
```
Run Full Import
```
php -f copy_data_from_old_magento.php usrdb_myproabr_live20201019 usrdb_myproabr_magento
```
Import specific tables only
```
php -f copy_data_from_old_magento.php usrdb_myproabr_live20201019 usrdb_myproabr_magento catalog_product_entity,catalog_product_entity_int,catalog_product_entity_decimal
```

# MyProduct Images Import

Script is located under `~/scripts`

Start import
```
./copy_images.sh
```