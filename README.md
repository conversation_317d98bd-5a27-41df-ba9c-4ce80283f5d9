myProduct
=========

This is the myProduct relaunch 2021.

## Windows Docker Setup

Under Windows you can use Docker using the Windows Subsystem for Linux (WSL).

### Windows Subsystem for Linux Installation

First of all, to prevent the virtual machine from hogging too many CPU cores and too much memroy, you should create a 
`%USERPROFILE%\.wslconfig` file with  the following content:

```
[wsl2]
memory=4GB
processors=2
```

Adjust the values accordingly to what you can make available on your system.

Basically follow [these steps](https://docs.docker.com/docker-for-windows/install/#wsl-2-backend) from the Docker documentation to install
WSL. Here are all the steps outlined:

1. Enable CPU virtualisation feature in your BIOS.
2. Upgrade to or install Windows 10 **Pro** 64-bit 1903 or higher.
3. [Enable the Windows Subsystem for Linux](https://docs.microsoft.com/en-gb/windows/wsl/install-win10#step-1---enable-the-windows-subsystem-for-linux).
4. [Enable the Virtual Machine feature](https://docs.microsoft.com/en-gb/windows/wsl/install-win10#step-3---enable-virtual-machine-feature).
5. [Install the Linux kernel update package](https://docs.microsoft.com/en-gb/windows/wsl/install-win10#step-4---download-the-linux-kernel-update-package).
6. [Set WSL 2 as your default version](https://docs.microsoft.com/en-gb/windows/wsl/install-win10#step-5---set-wsl-2-as-your-default-version).
7. [Install a Linux distribution](https://docs.microsoft.com/en-gb/windows/wsl/install-win10#step-6---install-your-linux-distribution-of-choice), e.g. Ubuntu.
8. Launch the Linux distribution and set a username and password.

### Docker Installation

After setting up WSL, follow [these steps](https://docs.docker.com/docker-for-windows/install/#install-docker-desktop-on-windows) to install
Docker. Make sure you enable **Install required Windows components for WSL 2**. Aftwards start Docker Desktop. If you want you can configure 
it to always start automatically.

### Checkout and run Magento Instance

1. Launch the Linux distribution. _Note:_ when the Linux system is running, you can access its files via Windows Explorer under `\\wsl$\<Distribution>`.
2. Copy your SSH key over into the `~/.ssh/` directory of the Linux distribution. Mind the directory permissions: https://community.perforce.com/s/article/6210
3. Execute `sudo apt-get update && sudo apt-get install keychain`.
4. Apply this solution: https://esc.sh/blog/ssh-agent-windows10-wsl2/. _Note:_ use `$HOSTNAME` instead of `$HOST` in the script.
5. Restart the Linux instance.
4. Clone the repository: `<NAME_EMAIL>:inspiredminds/myproduct.git` (e.g. directly in your user's home directory).
5. Enter the repository with `cd myproduct` and then type `code .` to open the repository in _VSCode_. This will allow you to edit the files as normal and VSCode's console will be the Linux system's console.
6. Create an `app/etc/env.php` with the content from [1].
7. Within the Linux system, build and run the docker containers: `docker-compose up -d --build`.
8. Connect to the mariadb server of the docker container via `root:root@localhost:13306` and copy the database from the live instance for example (also copy triggers and views etc.).
9. Copy `pub/media/catalog/` from the live instance for example to the container (exclude the `product/cache/` folder).
10. Log into the console of the elasticsearch container: `docker exec -it elasticsearch bash` and execute `bin/elasticsearch-plugin install analysis-phonetic`. Restart the container via Docker Desktop afterwards.
10. Log into the console of the web server container: `docker exec -it -u application web bash`.
11. Navigate to the Magento instance within the container with `cd app`.
12. Execute `composer install` to install all composer dependencies.
13. Run `bin/magento setup:upgrade`.
14. If you want to use Nginx as a reverse proxy, use the following configuration for example: [2] (uses the `myproduct.local` domain).

[1] `app/etc/env.php`:

```php
<?php
return [
    'backend' => [
        'frontName' => 'admin'
    ],
    'queue' => [
        'consumers_wait_for_messages' => 1
    ],
    'crypt' => [
        'key' => '5a0264a9d39ed0cde3ddf8cac4f073ef'
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => 'mariadb', // host.docker.internal:13306
                'dbname' => 'myproduct',
                'username' => 'root',
                'password' => 'root',
                'model' => 'mysql4',
                'engine' => 'innodb',
                'initStatements' => 'SET NAMES utf8;',
                'active' => '1',
                'driver_options' => [
                    1014 => false
                ]
            ]
        ]
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'developer',
    'lock' => [
        'provider' => 'db',
        'config' => [
            'prefix' => ''
        ]
    ],
    'directories' => [
        'document_root_is_pub' => true
    ],
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 0,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'compiled_config' => 1,
        'eav' => 1,
        'customer_notification' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 0,
        'config_webservice' => 1,
        'translate' => 1,
        'vertex' => 1
    ],
    'install' => [
        'date' => 'Thu, 04 Feb 2021 08:55:33 +0000'
    ],
    'system' => [
        'default' => [
           'payment' => [
                'stripe_payments_basic' => [
                    'stripe_mode' => 'test',
                ],
                'stripe_payments' => [
                    'active' => '0',
                ],
            ], 
            'web' => [
                'unsecure' => [
                    'base_url' => 'http://myproduct.local/',
                ],
                'secure' => [
                    'base_url' => 'https://myproduct.local/',
                ]
            ],
            'system' => [
                'full_page_cache' => [
                    'caching_application' => '1'
                ]
            ],
            'catalog' => [
                'search' => [
                    'elasticsearch7_server_hostname' => 'elasticsearch', // host.docker.internal
                ]
            ],
            'dev' => [
                'js' => [
                    'merge_files' => '0',
                    'enable_js_bundling' => '0',
                    'minify_files' => '0',
                ],
                'css' => [
                    'merge_css_files' => '0',
                    'minify_files' => '0',
                ]
            ]
        ]
    ]
];
```

[2] Nginx reverse proxy configuration:

```
server {
    listen 80;
    listen 443 ssl;
    server_name myproduct.local;

    location / {
        proxy_pass http://myproduct.local:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```


### VSCode

In order to get all IDE functionalities when having the remote project open in VSCode you will also need to install the appropriate 
extensions for VSCode in the Linux subsystem. While your remote project is open in VSCode, go to the Extensions (Ctrl+Shift+X) - you will 
see a "Install in WSL" button for reach extension that can be installed in the WSL and is not yet installed there. Do this for e.g. PHP 
Intelephense.


### PHP, NGINX

* `sudo mkdir /run/php`
* https://www.digitalocean.com/community/tutorials/how-to-install-linux-nginx-mysql-php-lemp-stack-ubuntu-18-04
* https://computingforgeeks.com/how-to-install-php-on-ubuntu/
* https://www.getpagespeed.com/server-setup/nginx-and-php-fpm-what-my-permissions-should-be
* `sudo vi /etc/php/7.4/fpm/pool.d/www.conf` » change user & owner to your Linux username
* `sudo usermod -a -G fritzmg www-data`
* https://www.unixmen.com/install-sourceguardian-php-extension-on-ubuntu-linux/
* https://support.vimp.com/en/faq/items/installing-the-sourceguardian-php-extension-on-ubuntu-20-with-php-74-and-nginx.html

## macOS/Linux (amd64/arm64) Docker Setup
For better performance on arm64/aarch64 based systems (Apple Silicon M1, M2, M3, ...) [markshust/docker-magento](https://github.com/markshust/docker-magento) can be used.

However, it can be used on most macOS and Linux machines, as it should support most operating systems and architectures.

Windows is also supported through the use of WSL2, while not recommended.
### Prerequisites
* Docker
* Composer in version 2.2.* (`composer self-update 2.2.22`)
* A database dump of the live, staging or dev database
* The `pub/media/catalog` folder from live

### Setup
#### 1. Clone the GitLab repository
Clone the myProduct GitLab repo to `~/myproduct`.
#### 2. Install composer dependencies
Be sure to downgrade composer to version 2.2.22 prior to executing `composer install`.
To do this, run `composer self-update 2.2.22` anywhere on your system. We won't need to downgrade composer in the docker container, as it uses version 2.2.9.

Execute `composer install` in the `myproduct` folder.
#### 3. Copy media files
While composer installs dependencies, you can grab your `pub/media/catalog` folder you downloaded earlier from the live instance and extract its contents into the corresponding directory in your local `myproduct` repository.
#### 4. Create an env.php file
The content of your `app/etc/env.php` file should look identical to this:

```php
<?php
return [
    'backend' => [
        'frontName' => 'admin'
    ],
    'queue' => [
        'consumers_wait_for_messages' => 1
    ],
    'crypt' => [
        'key' => '5a0264a9d39ed0cde3ddf8cac4f073ef'
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => 'db', // host.docker.internal:13306
                'dbname' => 'magento',
                'username' => 'magento',
                'password' => 'magento',
                'model' => 'mysql4',
                'engine' => 'innodb',
                'initStatements' => 'SET NAMES utf8;',
                'active' => '1',
                'driver_options' => [
                    1014 => false
                ]
            ]
        ]
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'directories' => [
        'document_root_is_pub' => true
    ],
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 0,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'compiled_config' => 1,
        'eav' => 1,
        'customer_notification' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 0,
        'config_webservice' => 1,
        'translate' => 1,
        'vertex' => 1
    ],
    'install' => [
        'date' => 'Thu, 04 Feb 2021 08:55:33 +0000'
    ],
    'system' => [
        'default' => [
            'payment' => [
                'stripe_payments_basic' => [
                    'stripe_mode' => 'test',
                ],
                'stripe_payments' => [
                    'active' => '0',
                ],
            ],
            'system' => [
                'full_page_cache' => [
                    'caching_application' => '1'
                ]
            ],
            'dev' => [
                'js' => [
                    'merge_files' => '0',
                    'enable_js_bundling' => '0',
                    'minify_files' => '0',
                ],
                'css' => [
                    'merge_css_files' => '0',
                    'minify_files' => '0',
                ]
            ]
        ]
    ]
];
```

It's similar to the WSL2 Docker env.php file, however elasticsearch and domain settings have been removed.
These will be set in a later step.
#### 5. Get docker-magento
`cd ..` out of your `myproduct` directory.

Create a new directory called `magento` with `mkdir ~/magento` and `cd` into it.

Get the required files from the markshust/docker-magento repository:

```
git init -qqq
git remote add origin https://github.com/markshust/docker-magento
git fetch origin -qqq
git checkout tags/41.1.0 -- compose
mv compose/* ./
mv compose/.gitignore ./
mv compose/.vscode ./
rm -rf compose .git
git init
```

Tag `41.1.0` is used here, as it is the last version to use PHP 7.4 and elasticsearch.

The above commands will download the required files and initialize a new git repository in your `~/magento` directory.
#### 6. Copy directories
From here, copy the contents of your `~/myproduct` directory into `~/magento/src`:

`cp -R ~/myproduct src`
#### 7. Fire up the containers
Run the command `docker-compose -f docker-compose.yml up -d` to start the containers for the first time.
#### 8. Copy files to the container
The command `bin/copytocontainer --all` will copy the `src` directory to the Docker container and restart it afterwards.
#### 9. Import the database
`bin/mysql < ~/database_dump.sql` will import your database to the `db` container.
Restart your containers again with `bin/restart`.
#### 10. Import the magento config
Run `bin/magento app:config:import`.
#### 11. Configure your local test domain
Run `bin/setup-domain magento.test`. This should also set the domain in magento.

The command will prompt for your `sudo` password, add an entry to `/etc/hosts` and install a certificate on your local machine.

Restart the containers again afterwards.
#### 12. Deploy magento
```
bin/magento setup:upgrade
bin/magento setup:static-content:deploy -f
bin/magento setup:di:compile
```
This will throw an error:
`ElasticSuite : Unable to validate connection to Elasticsearch server : No alive nodes found in your cluster`

We will let it finish and configure the elasticsearch hostname in the next step.
#### 13. Configure Elasticsearch
Now, you should already be able to access your site at `magento.test`.

Go to the admin panel, and under Stores > Configuration > ElasticSuite > Base Settings > Elasticsearch Client, set the hostname (Servers List) to `elasticsearch:9200`.

Afterwards, run `bin/magento setup:upgrade` one more time. There should be no error thrown this time.
#### 14. Check status
Run `bin/status` to check if all of the 7 containers are up and running.
### Finished Installation
Installation is finished at this point, you should be left with a functioning instance of magento.

For complete command reference, see the docker-magento project on GitHub.

Commands with `bin/` should always be run from your `~/magento` directory, NOT from `src`. Otherwise, they will not be executed on the container itself, but rather on your local machine.
