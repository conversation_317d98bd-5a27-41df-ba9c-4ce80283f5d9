# get nvm working
. ~/.nvm/nvm.sh

build() {
  for theme_dir in \
    "vendor/creativestyle/theme-ssnoe" \
    "vendor/creativestyle/theme-vinotaria" \
    "vendor/creativestyle/theme-asm" \
    "vendor/creativestyle/theme-manner" \
    "vendor/creativestyle/theme-droetker" \
    "vendor/creativestyle/theme-pago" \
    "vendor/creativestyle/theme-davidfussenegger"
  do
    echo "Cleaning up ${theme_dir}..."
    # Delete the existing node_modules folder if it exists
    rm -rf "${theme_dir}/node_modules"

    echo "Installing dependencies for ${theme_dir}..."
    # Run yarn install inside the theme directory
    (cd "$theme_dir" && yarn)
  done
}

echo "Setting up Node.js version..."
nvm use

echo "Installing dependencies for theme-creativeshop..."
(cd vendor/creativestyle/theme-creativeshop; yarn)

echo "Installing dependencies for theme-myproduct..."
(cd vendor/creativestyle/theme-myproduct; yarn)

echo "Building other themes..."
build &
wait

echo "All theme dependencies installed"
