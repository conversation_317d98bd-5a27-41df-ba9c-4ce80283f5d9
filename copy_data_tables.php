<?php

define('PRIMARY', 'primary');
define('NULLABLE', 'nullable');
define('UNIQUE_KEY', 'unique_key');

$mappingTables = [
    'mapping_catalog_product_entity' => [
        'create_query' => '
            CREATE TABLE mapping_catalog_product_entity 
            select a.entity_id, a.sku
            from catalog_product_entity a 
                inner join eav_attribute_set b on a.attribute_set_id = b.attribute_set_id 
                inner join mapping_eav_attribute_set c on b.attribute_set_name = c.attribute_set_name
            where a.type_id <> \\"giftvoucher\\";
            alter table mapping_catalog_product_entity add primary key (entity_id)
        ',
    ],
    'mapping_eav_attribute_option' => [
        'create_query' => '
            CREATE TABLE mapping_eav_attribute_option 
            select a.option_id  
            from eav_attribute_option a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code 
            where option_id not in (1,2);
        ',
    ],
    'mapping_catalog_product_option' => [
        'create_query' => '
            CREATE TABLE mapping_catalog_product_option 
            select a.option_id
            from catalog_product_option a 
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id
        ',
    ],
    'mapping_catalog_product_option_type_value' => [
        'create_query' => '
            CREATE TABLE mapping_catalog_product_option_type_value 
            select a.option_type_id
            from catalog_product_option_type_value a
                inner join mapping_catalog_product_option b on a.option_id = b.option_id
        ',
    ],
    'mapping_catalog_product_link' => [
        'create_query' => '
            CREATE TABLE mapping_catalog_product_link 
            select a.link_id
            from catalog_product_link a
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id 
                inner join mapping_catalog_product_entity c on a.linked_product_id = c.entity_id 
        ',
    ],
    'mapping_catalog_product_super_attribute' => [
        'create_query' => '
            CREATE TABLE mapping_catalog_product_super_attribute 
            select a.product_super_attribute_id
            from catalog_product_super_attribute a
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id 
                inner join eav_attribute c on a.attribute_id = c.attribute_id 
                inner join mapping_eav_attribute d on c.attribute_code = d.attribute_code 
        ',
    ],
    'mapping_customer_entity' => [
        'create_query' => '
            CREATE TABLE mapping_customer_entity 
            select a.entity_id
            from customer_entity a 
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code
            where a.website_id <> 4
            group by a.email;
            alter table mapping_customer_entity add primary key (entity_id)
        ',
    ],
    'mapping_quote' => [
        'create_query' => '
            CREATE TABLE mapping_quote 
            SELECT a.entity_id
            from quote a
                inner join store b on a.store_id = b.store_id
                inner join mapping_store c on b.code = c.code 
            order by a.entity_id
        ',
    ],
    'mapping_quote_item' => [
        'create_query' => '
            CREATE TABLE mapping_quote_item 
            select  
                a.item_id
            from quote_item a 
                inner join mapping_quote b on a.quote_id = b.entity_id 
                inner join store c on a.store_id = c.store_id 
                inner join mapping_store d on c.code = d.code
            order by a.item_id
        ',
    ],
    'mapping_quote_address' => [
        'create_query' => '
            CREATE TABLE mapping_quote_address 
            select a.address_id
            from quote_address a
                inner join mapping_quote b on a.quote_id = b.entity_id
        ',
    ],
];

$tables = [
    /*
    'catalog_category_entity' => [
        'export_query' => '
            SELECT entity_id, attribute_set_id, parent_id, path, position, level, children_count 
            FROM catalog_category_entity
            WHERE entity_id NOT IN (1,2)
        ',
        'columns' => ['entity_id' => [PRIMARY], 'attribute_set_id' => [], 'parent_id' => [], 'path' => [], 'position' => [], 'level' => [], 'children_count' => []],
    ],
    'catalog_category_entity_decimal' => [
        'export_query' => '
            select 
                c.attribute_id,
                e.store_id,
                a.entity_id, 
                ifnull(a.value, \\"\\") as value
            from catalog_category_entity_decimal a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
                inner join store d on a.store_id = d.store_id 
                inner join mapping_store e on d.code = e.code 
                inner join catalog_category_entity f on a.entity_id = f.entity_id
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'entity_id' => [UNIQUE_KEY], 'value' => [NULLABLE]],
    ],
    'catalog_category_entity_int' => [
        'export_query' => '
            select 
                c.attribute_id,
                e.store_id,
                a.entity_id, 
                ifnull(a.value, \\"\\") as value
            from catalog_category_entity_int a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
                inner join store d on a.store_id = d.store_id 
                inner join mapping_store e on d.code = e.code 
                inner join catalog_category_entity f on a.entity_id = f.entity_id
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'entity_id' => [UNIQUE_KEY], 'value' => [NULLABLE]],
    ],
    'catalog_category_entity_text' => [
        'export_query' => '
            select 
                c.attribute_id,
                e.store_id,
                a.entity_id, 
                ifnull(a.value, \\"\\") as value
            from catalog_category_entity_text a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
                inner join store d on a.store_id = d.store_id 
                inner join mapping_store e on d.code = e.code 
                inner join catalog_category_entity f on a.entity_id = f.entity_id
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'entity_id' => [UNIQUE_KEY], 'value' => [NULLABLE]],
    ],
    'catalog_category_entity_varchar' => [
        'export_query' => '
            select 
                c.attribute_id,
                e.store_id,
                a.entity_id, 
                ifnull(a.value, \\"\\") as value
            from catalog_category_entity_varchar a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
                inner join store d on a.store_id = d.store_id 
                inner join mapping_store e on d.code = e.code 
                inner join catalog_category_entity f on a.entity_id = f.entity_id
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'entity_id' => [UNIQUE_KEY], 'value' => [NULLABLE]],
    ],
    */
    /*
    'catalog_product_entity' => [
        'export_query' => '
            select a.entity_id, c.attribute_set_id, a.type_id, a.sku, a.has_options, a.required_options
            from catalog_product_entity a 
                inner join eav_attribute_set b on a.attribute_set_id = b.attribute_set_id 
                inner join mapping_eav_attribute_set c on b.attribute_set_name = c.attribute_set_name
            where a.type_id <> \\"giftvoucher\\"
        ',
        'columns' => ['entity_id' => [PRIMARY], 'attribute_set_id' => [], 'type_id' => [], 'sku' => [NULLABLE], 'has_options' => [], 'required_options' => []],
    ],
    'catalog_product_entity_datetime' => [
        'export_query' => '
            select 
                c.attribute_id,
                e.store_id,
                a.entity_id, 
                ifnull(a.value, \\"\\") as value
            from catalog_product_entity_datetime a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
                inner join store d on a.store_id = d.store_id 
                inner join mapping_store e on d.code = e.code 
                inner join mapping_catalog_product_entity f on a.entity_id = f.entity_id 
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'entity_id' => [UNIQUE_KEY], 'value' => [NULLABLE]],
    ],
    'catalog_product_entity_decimal' => [
        'export_query' => '
            select 
                c.attribute_id,
                e.store_id,
                a.entity_id, 
                ifnull(a.value, \\"\\") as value
            from catalog_product_entity_decimal a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
                inner join store d on a.store_id = d.store_id 
                inner join mapping_store e on d.code = e.code 
                inner join mapping_catalog_product_entity f on a.entity_id = f.entity_id 
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'entity_id' => [UNIQUE_KEY], 'value' => [NULLABLE]],
    ],
    'catalog_product_entity_int' => [
        'export_query' => '
            select 
                c.attribute_id,
                e.store_id,
                a.entity_id, 
                ifnull(a.value, \\"\\") as value
            from catalog_product_entity_int a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
                inner join store d on a.store_id = d.store_id 
                inner join mapping_store e on d.code = e.code 
                inner join mapping_catalog_product_entity f on a.entity_id = f.entity_id 
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'entity_id' => [UNIQUE_KEY], 'value' => [NULLABLE]],
    ],
    'catalog_product_entity_text' => [
        'export_query' => '
            select 
                c.attribute_id,
                e.store_id,
                a.entity_id, 
                ifnull(a.value, \\"\\") as value
            from catalog_product_entity_text a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
                inner join store d on a.store_id = d.store_id 
                inner join mapping_store e on d.code = e.code 
                inner join mapping_catalog_product_entity f on a.entity_id = f.entity_id 
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'entity_id' => [UNIQUE_KEY], 'value' => [NULLABLE]],
    ],
    'catalog_product_entity_varchar' => [
        'export_query' => '
            select 
                c.attribute_id,
                e.store_id,
                a.entity_id, 
                ifnull(a.value, \\"\\") as value
            from catalog_product_entity_varchar a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
                inner join store d on a.store_id = d.store_id 
                inner join mapping_store e on d.code = e.code 
                inner join mapping_catalog_product_entity f on a.entity_id = f.entity_id 
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'entity_id' => [UNIQUE_KEY], 'value' => [NULLABLE]],
    ],
    'catalog_category_product' => [
        'export_query' => '
            select category_id, product_id, a.position 
            from catalog_category_product a 
                inner join catalog_category_entity b on a.category_id = b.entity_id 
                inner join mapping_catalog_product_entity c on a.product_id = c.entity_id
        ',
        'columns' => ['category_id' => [PRIMARY, UNIQUE_KEY], 'product_id' => [PRIMARY, UNIQUE_KEY], 'position' => []],
    ],
    'catalog_product_website' => [
        'export_query' => '
            select 
                product_id,
                case website_id 
                    when 10 then 2
                    else 1
                end as website_id
            from catalog_product_website a
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id
            where website_id in (1,5,10)
        ',
        'columns' => ['product_id' => [PRIMARY], 'website_id' => [PRIMARY]],
    ],
    */
    'cataloginventory_stock_item' => [
        'export_query' => '
            select a.item_id, a.product_id, a.stock_id, ifnull(a.qty, \\"\\") as qty, a.min_qty, a.use_config_min_qty, a.is_qty_decimal, a.backorders, a.use_config_backorders, a.min_sale_qty, a.use_config_min_sale_qty, a.max_sale_qty, a.use_config_max_sale_qty, a.is_in_stock, a.low_stock_date, ifnull(a.notify_stock_qty, \\"\\") as notify_stock_qty, a.use_config_notify_stock_qty, a.manage_stock, a.use_config_manage_stock, a.stock_status_changed_auto, a.use_config_qty_increments, a.qty_increments, a.use_config_enable_qty_inc, a.enable_qty_increments, a.is_decimal_divided, a.website_id 
            from cataloginventory_stock_item a
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id
        ',
        'columns' => ['item_id' => [PRIMARY], 'product_id' => [UNIQUE_KEY], 'stock_id' => [UNIQUE_KEY], 'qty' => [NULLABLE], 'min_qty' => [], 'use_config_min_qty' => [], 'is_qty_decimal' => [], 'backorders' => [], 'use_config_backorders' => [], 'min_sale_qty' => [], 'use_config_min_sale_qty' => [], 'max_sale_qty' => [], 'use_config_max_sale_qty' => [], 'is_in_stock' => [], 'low_stock_date' => [], 'notify_stock_qty' => [NULLABLE], 'use_config_notify_stock_qty' => [], 'manage_stock' => [], 'use_config_manage_stock' => [], 'stock_status_changed_auto' => [], 'use_config_qty_increments' => [], 'qty_increments' => [], 'use_config_enable_qty_inc' => [], 'enable_qty_increments' => [], 'is_decimal_divided' => [], 'website_id' => []],
    ],
    'cataloginventory_stock_status' => [
        'export_query' => '
            select * from cataloginventory_stock_status
        ',
        'columns' => ['product_id' => [PRIMARY], 'website_id' => [PRIMARY], 'stock_id' => [PRIMARY], 'qty' => [], 'stock_status' => []],
    ],
    /*
    'catalog_product_entity_media_gallery' => [
        'export_query' => '
            select a.value_id, c.attribute_id, ifnull(a.value, \\"\\") as value, a.media_type, a.disabled
            from catalog_product_entity_media_gallery a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id 
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code and b.entity_type_id = c.entity_type_id 
        ',
        'columns' => ['value_id' => [PRIMARY], 'attribute_id' => [], 'value' => [NULLABLE], 'media_type' => [], 'disabled' => []],
        'pre_import_query' => 'delete from catalog_product_entity_media_gallery',
    ],
    'catalog_product_entity_media_gallery_value' => [
        'export_query' => '
            select a.value_id, c.store_id, a.entity_id, ifnull(a.label, \\"\\") as label, ifnull(a.position, \\"\\") as position, a.disabled, a.record_id
            from catalog_product_entity_media_gallery_value a
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code 
                inner join mapping_catalog_product_entity d on a.entity_id = d.entity_id 
        ',
        'columns' => ['value_id' => [], 'store_id' => [], 'entity_id' => [], 'label' => [NULLABLE], 'position' => [NULLABLE], 'disabled' => [], 'record_id' => [PRIMARY]],
    ],
    'catalog_product_entity_media_gallery_value_to_entity' => [
        'export_query' => '
            select * 
            from catalog_product_entity_media_gallery_value_to_entity a
                inner join mapping_catalog_product_entity b on a.entity_id = b.entity_id 
        ',
        'columns' => ['value_id' => [PRIMARY, UNIQUE_KEY], 'entity_id' => [PRIMARY, UNIQUE_KEY]],
    ],
    */
    'eav_attribute_option' => [
        'export_query' => '
            select a.option_id, c.attribute_id, a.sort_order  
            from eav_attribute_option a 
                inner join eav_attribute b on a.attribute_id = b.attribute_id
                inner join mapping_eav_attribute c on b.attribute_code = c.attribute_code 
            where option_id not in (1,2)
        ',
        'columns' => ['option_id' => [PRIMARY], 'attribute_id' => [], 'sort_order' => []],
        'pre_import_query' => 'delete from eav_attribute_option',
    ],
    'eav_attribute_option_value' => [
        'export_query' => '
            select a.value_id, a.option_id, c.store_id, ifnull(a.value, \\"\\") as value
            from eav_attribute_option_value a 
                inner join store b on a.store_id = b.store_id
                inner join mapping_store c on b.code = c.code 
                inner join mapping_eav_attribute_option d on a.option_id = d.option_id
        ',
        'columns' => ['value_id' => [PRIMARY], 'option_id' => [], 'store_id' => [], 'value' => [NULLABLE]],
    ],
    'eav_attribute_option_swatch' => [
        'export_query' => '
            select a.swatch_id, a.option_id, c.store_id, a.type, ifnull(a.value, \\"\\") as value 
            from eav_attribute_option_swatch a 
                inner join store b on a.store_id = b.store_id
                inner join mapping_store c on b.code = c.code 
                inner join mapping_eav_attribute_option d on a.option_id = d.option_id
        ',
        'columns' => ['swatch_id' => [PRIMARY], 'option_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'type' => [], 'value' => [NULLABLE]],
    ],
    /*
    'catalog_product_bundle_option' => [
        'export_query' => '
            select a.option_id, a.parent_id, a.required, 0, ifnull(a.type, \\"\\") as type
            from catalog_product_bundle_option a 
                inner join mapping_catalog_product_entity b on a.parent_id = b.entity_id
        ',
        'columns' => ['option_id' => [PRIMARY], 'parent_id' => [], 'required' => [], 'position' => [], 'type' => [NULLABLE]],
    ],
    'catalog_product_bundle_option_value' => [
        'export_query' => '
            select a.option_id, a.parent_product_id, c.store_id, ifnull(a.title, \\"\\") as title 
            from catalog_product_bundle_option_value a 
                inner join store b on a.store_id = b.store_id
                inner join mapping_store c on b.code = c.code 
                inner join catalog_product_bundle_option d on a.option_id = d.option_id 
        ',
        'columns' => ['option_id' => [UNIQUE_KEY], 'parent_product_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'title' => [NULLABLE]],
    ],
    'catalog_product_bundle_selection' => [
        'export_query' => '
            select a.selection_id, a.option_id, a.parent_product_id, a.product_id, a.position, a.is_default, a.selection_price_type, a.selection_price_value, ifnull(a.selection_qty, \\"\\") as selection_qty, a.selection_can_change_qty
            from catalog_product_bundle_selection a
                inner join catalog_product_bundle_option b on a.option_id = b.option_id
                inner join mapping_catalog_product_entity d on a.product_id = d.entity_id 
        ',
        'columns' => ['selection_id' => [PRIMARY], 'option_id' => [], 'parent_product_id' => [], 'product_id' => [], 'position' => [], 'is_default' => [], 'selection_price_type' => [], 'selection_price_value' => [], 'selection_qty' => [NULLABLE], 'selection_can_change_qty' => []],
    ],
    'catalog_product_super_link' => [
        'export_query' => '
            select a.product_id, a.parent_id from catalog_product_super_link a 
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id 
                inner join mapping_catalog_product_entity c on a.parent_id = c.entity_id
        ',
        'columns' => ['product_id' => [UNIQUE_KEY], 'parent_id' => [UNIQUE_KEY]],
    ],
    'catalog_product_super_attribute' => [
        'export_query' => '
            select a.product_super_attribute_id, a.product_id, d.attribute_id, a.position, a.identify_image
            from catalog_product_super_attribute a
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id 
                inner join eav_attribute c on a.attribute_id = c.attribute_id 
                inner join mapping_eav_attribute d on c.attribute_code = d.attribute_code 
        ',
        'columns' => ['product_super_attribute_id' => [PRIMARY], 'product_id' => [UNIQUE_KEY], 'attribute_id' => [UNIQUE_KEY], 'position' => [], 'identify_image' => []],
        'pre_import_query' => 'delete from catalog_product_super_attribute',
    ],
    'catalog_product_super_attribute_label' => [
        'export_query' => '
            select a.product_super_attribute_id, c.store_id, ifnull(a.use_default, \\"\\") as use_default, ifnull(a.value, \\"\\") as value 
            from catalog_product_super_attribute_label a
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code 
                inner join mapping_catalog_product_super_attribute d on a.product_super_attribute_id = d.product_super_attribute_id 
        ',
        'columns' => ['product_super_attribute_id' => [UNIQUE_KEY], 'store_id' => [UNIQUE_KEY], 'use_default' => [NULLABLE], 'value' => [NULLABLE]],
    ],
    'catalog_product_relation' => [
        'export_query' => '
            select 
                a.parent_id, a.child_id
            from catalog_product_relation a
                inner join mapping_catalog_product_entity b on a.parent_id = b.entity_id 
                inner join mapping_catalog_product_entity c on a.child_id = c.entity_id 
        ',
        'columns' => ['parent_id' => [PRIMARY],'child_id' => [PRIMARY]],
    ],
    'catalog_product_option' => [
        'export_query' => '
            select 
                a.option_id, a.product_id, ifnull(a.type, \\"\\") as type, a.is_require, ifnull(a.sku, \\"\\") as sku, ifnull(a.max_characters, \\"\\") as max_characters, ifnull(a.file_extension, \\"\\") as file_extension, ifnull(a.image_size_x, \\"\\") as image_size_x, ifnull(a.image_size_y, \\"\\") as image_size_y, a.sort_order
            from catalog_product_option a 
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id
        ',
        'columns' => ['option_id' => [PRIMARY],'product_id' => [],'type' => [NULLABLE],'is_require' => [],'sku' => [NULLABLE],'max_characters' => [NULLABLE],'file_extension' => [NULLABLE],'image_size_x' => [NULLABLE],'image_size_y' => [NULLABLE],'sort_order' => []],
    ],
    'catalog_product_option_title' => [
        'export_query' => '
            select 
                a.option_title_id, a.option_id, d.store_id, ifnull(a.title, \\"\\") as title
            from catalog_product_option_title a
                inner join mapping_catalog_product_option b on a.option_id = b.option_id
                inner join store c on a.store_id = c.store_id 
	            inner join mapping_store d on c.code = d.code 
        ',
        'columns' => ['option_title_id' => [PRIMARY],'option_id' => [UNIQUE_KEY],'store_id' => [UNIQUE_KEY],'title' => [NULLABLE]],
    ],
    'catalog_product_option_price' => [
        'export_query' => '
            select 
                a.option_price_id, a.option_id, d.store_id, a.price, a.price_type
            from catalog_product_option_price a
                inner join mapping_catalog_product_option b on a.option_id = b.option_id
                inner join store c on a.store_id = c.store_id 
	            inner join mapping_store d on c.code = d.code 
        ',
        'columns' => ['option_price_id' => [PRIMARY],'option_id' => [UNIQUE_KEY],'store_id' => [UNIQUE_KEY],'price' => [],'price_type' => []],
    ],
    'catalog_product_option_type_value' => [
        'export_query' => '
            select 
                a.option_type_id, a.option_id, ifnull(a.sku, \\"\\") as sku, a.sort_order
            from catalog_product_option_type_value a
                inner join mapping_catalog_product_option b on a.option_id = b.option_id
        ',
        'columns' => ['option_type_id' => [PRIMARY],'option_id' => [],'sku' => [NULLABLE],'sort_order' => []],
    ],
    'catalog_product_option_type_title' => [
        'export_query' => '
            select 
                a.option_type_title_id, a.option_type_id, d.store_id, ifnull(a.title, \\"\\") as title
            from catalog_product_option_type_title a
                inner join mapping_catalog_product_option_type_value b on a.option_type_id = b.option_type_id
                inner join store c on a.store_id = c.store_id 
	            inner join mapping_store d on c.code = d.code
        ',
        'columns' => ['option_type_title_id' => [PRIMARY],'option_type_id' => [UNIQUE_KEY],'store_id' => [UNIQUE_KEY],'title' => [NULLABLE]],
    ],
    'catalog_product_option_type_price' => [
        'export_query' => '
            select 
                a.option_type_price_id, a.option_type_id, d.store_id, a.price, a.price_type
            from catalog_product_option_type_price a
                inner join mapping_catalog_product_option_type_value b on a.option_type_id = b.option_type_id
                inner join store c on a.store_id = c.store_id 
	            inner join mapping_store d on c.code = d.code
        ',
        'columns' => ['option_type_price_id' => [PRIMARY],'option_type_id' => [UNIQUE_KEY],'store_id' => [UNIQUE_KEY],'price' => [],'price_type' => []],
    ],
    'catalog_product_link' => [
        'export_query' => '
            select 
                a.link_id, a.product_id, a.linked_product_id, a.link_type_id
            from catalog_product_link a
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id 
                inner join mapping_catalog_product_entity c on a.linked_product_id = c.entity_id 
            order by a.link_id
        ',
        'columns' => ['link_id' => [PRIMARY],'product_id' => [UNIQUE_KEY],'linked_product_id' => [UNIQUE_KEY],'link_type_id' => [UNIQUE_KEY]],
        'pre_import_query' => 'delete from catalog_product_link',
    ],
    'catalog_product_link_attribute_int' => [
        'export_query' => '
            select 
                a.value_id, 
                case a.product_link_attribute_id
                    when 2 then 4
                    when 3 then 5
                    when 4 then 2
                    when 5 then 3
                    else ifnull(a.product_link_attribute_id, \\"\\")
                end as product_link_attribute_id,
                a.link_id, 
                a.value
            from catalog_product_link_attribute_int a
                inner join mapping_catalog_product_link b on a.link_id = b.link_id 
        ',
        'columns' => ['value_id' => [PRIMARY],'product_link_attribute_id' => [UNIQUE_KEY, NULLABLE],'link_id' => [UNIQUE_KEY],'value' => []],
    ],
    'catalog_product_link_attribute_decimal' => [
        'export_query' => '
            select 
                a.value_id, 
                case a.product_link_attribute_id
                    when 2 then 4
                    when 3 then 5
                    when 4 then 2
                    when 5 then 3
                    else ifnull(a.product_link_attribute_id, \\"\\")
                end as product_link_attribute_id,
                a.link_id, 
                a.value
            from catalog_product_link_attribute_decimal a
                inner join mapping_catalog_product_link b on a.link_id = b.link_id 
        ',
        'columns' => ['value_id' => [PRIMARY],'product_link_attribute_id' => [UNIQUE_KEY, NULLABLE],'link_id' => [UNIQUE_KEY],'value' => []],
    ],
    */
    // tax
    'tax_class' => [
        'export_query' => '
            select * from tax_class
        ',
        'columns' => ['class_id' => [PRIMARY], 'class_name' => [], 'class_type' => []],
    ],
    'tax_calculation_rule' => [
        'export_query' => '
        select
            a.tax_calculation_rule_id, a.code, a.priority, a.position, a.calculate_subtotal
        from tax_calculation_rule a
        ',
        'columns' => ['tax_calculation_rule_id' => [PRIMARY],'code' => [],'priority' => [],'position' => [],'calculate_subtotal' => []],
    ],
    'tax_calculation_rate' => [
        'export_query' => '
        select
            a.tax_calculation_rate_id, a.tax_country_id, a.tax_region_id, ifnull(a.tax_postcode, \\"\\") as tax_postcode, a.code, a.rate, ifnull(a.zip_is_range, \\"\\") as zip_is_range, ifnull(a.zip_from, \\"\\") as zip_from, ifnull(a.zip_to, \\"\\") as zip_to
        from tax_calculation_rate a
        ',
        'columns' => ['tax_calculation_rate_id' => [PRIMARY],'tax_country_id' => [],'tax_region_id' => [],'tax_postcode' => [NULLABLE],'code' => [],'rate' => [],'zip_is_range' => [NULLABLE],'zip_from' => [NULLABLE],'zip_to' => [NULLABLE]],
    ],
    'tax_calculation' => [
        'export_query' => '
            select 
                a.tax_calculation_id, a.tax_calculation_rate_id, a.tax_calculation_rule_id, a.customer_tax_class_id, a.product_tax_class_id
            from tax_calculation a
        ',
        'columns' => ['tax_calculation_id' => [PRIMARY],'tax_calculation_rate_id' => [],'tax_calculation_rule_id' => [],'customer_tax_class_id' => [],'product_tax_class_id' => []],
    ],
    // customer
    'customer_group' => [
        'export_query' => '
            select * from customer_group
        ',
        'columns' => ['customer_group_id' => [PRIMARY], 'customer_group_code' => [], 'tax_class_id' => []],
    ],
    'customer_entity' => [
        'export_query' => '
            select a.entity_id, 
                case a.website_id
                    when 10 then 2
                    when 11 then 2
                    when 5 then 1
                    else a.website_id 
                end as website_id,
                ifnull(a.email, \\"\\") as email, 
                a.group_id, 
                c.store_id, 
                a.created_at, 
                a.updated_at, 
                a.is_active, 
                a.disable_auto_group_change, 
                ifnull(a.created_in, \\"\\") as created_in, 
                ifnull(a.prefix, \\"\\") as prefix, 
                ifnull(a.firstname, \\"\\") as firstname, 
                ifnull(a.middlename, \\"\\") as middlename, 
                ifnull(a.lastname, \\"\\") as lastname, 
                ifnull(a.suffix, \\"\\") as suffix, 
                ifnull(a.dob, \\"\\") as dob, 
                ifnull(a.password_hash, \\"\\") as password_hash, 
                ifnull(a.rp_token, \\"\\") as rp_token, 
                ifnull(a.rp_token_created_at, \\"\\") as rp_token_created_at, 
                ifnull(a.default_billing, \\"\\") as default_billing, 
                ifnull(a.default_shipping, \\"\\") as default_shipping, 
                ifnull(a.taxvat, \\"\\") as taxvat, 
                ifnull(a.confirmation, \\"\\") as confirmation, 
                ifnull(a.gender, \\"\\") as gender,  
                ifnull(a.failures_num, \\"\\") as failures_num, 
                ifnull(a.first_failure, \\"\\") as first_failure, 
                ifnull(a.lock_expires, \\"\\") as lock_expires 
            from customer_entity a 
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code
            where a.website_id <> 4
            group by a.email
            order by a.entity_id 
        ',
        'columns' => ['entity_id' => [PRIMARY], 'website_id' => [UNIQUE_KEY, NULLABLE], 'email' => [UNIQUE_KEY, NULLABLE], 'group_id' => [], 'store_id' => [], 'created_at' => [], 'updated_at' => [], 'is_active' => [], 'disable_auto_group_change' => [], 'created_in' => [NULLABLE], 'prefix' => [NULLABLE], 'firstname' => [NULLABLE], 'middlename' => [NULLABLE], 'lastname' => [NULLABLE], 'suffix' => [NULLABLE], 'dob' => [NULLABLE], 'password_hash' => [NULLABLE], 'rp_token' => [NULLABLE], 'rp_token_created_at' => [NULLABLE], 'default_billing' => [NULLABLE], 'default_shipping' => [NULLABLE], 'taxvat' => [NULLABLE], 'confirmation' => [NULLABLE], 'gender' => [NULLABLE], 'failures_num' => [NULLABLE], 'first_failure' => [NULLABLE], 'lock_expires' => [NULLABLE]],
    ],
    'customer_address_entity' => [
        'export_query' => '
            select 
                a.entity_id, 
                ifnull(a.parent_id, \\"\\") as parent_id, 
                a.created_at, 
                a.updated_at, 
                a.is_active, 
                a.city, 
                ifnull(a.company, \\"\\") as company, 
                a.country_id, 
                ifnull(a.fax, \\"\\") as fax, 
                a.firstname, 
                a.lastname, 
                ifnull(a.middlename, \\"\\") as middlename, 
                ifnull(a.postcode, \\"\\") as postcode, 
                ifnull(a.prefix, \\"\\") as prefix,
                case a.region 
                    when \\"Voralberg\\" then \\"Vorarlberg\\"
                    else ifnull(a.region, \\"\\") 
                end as region,
                ifnull(a.region_id, \\"\\") as region_id, 
                a.street, 
                ifnull(a.suffix, \\"\\") as suffix, 
                a.telephone, 
                ifnull(a.vat_id, \\"\\") as vat_id
            from customer_address_entity a 
                inner join mapping_customer_entity b on a.parent_id = b.entity_id 
            order by entity_id 
        ',
        'columns' => ['entity_id' => [PRIMARY], 'parent_id' => [NULLABLE], 'created_at' => [], 'updated_at' => [], 'is_active' => [], 'city' => [], 'company' => [NULLABLE], 'country_id' => [], 'fax' => [NULLABLE], 'firstname' => [], 'lastname' => [], 'middlename' => [NULLABLE], 'postcode' => [NULLABLE], 'prefix' => [NULLABLE], 'region' => [NULLABLE], 'region_id' => [NULLABLE], 'street' => [], 'suffix' => [NULLABLE], 'telephone' => [], 'vat_id'=> [NULLABLE]],
    ],
    // quote
    'quote' => [
        'export_query' => '
            SELECT 
                a.entity_id,
                c.store_id,
                a.created_at,
                a.updated_at,
                ifnull(a.converted_at, \\"\\") as converted_at,
                a.is_active,
                a.is_virtual,
                a.is_multi_shipping,
                a.items_count,
                a.items_qty,
                a.orig_order_id,
                a.store_to_base_rate,
                a.store_to_quote_rate,
                ifnull(a.base_currency_code, \\"\\") as base_currency_code,
                ifnull(a.store_currency_code, \\"\\") as store_currency_code,
                ifnull(a.quote_currency_code, \\"\\") as quote_currency_code,
                a.grand_total,
                a.base_grand_total,
                ifnull(a.checkout_method, \\"\\") as checkout_method,
                ifnull(a.customer_id, \\"\\") as customer_id,
                ifnull(a.customer_tax_class_id, \\"\\") as customer_tax_class_id,
                a.customer_group_id,
                ifnull(a.customer_email, \\"\\") as customer_email,
                ifnull(a.customer_prefix, \\"\\") as customer_prefix,
                ifnull(a.customer_firstname, \\"\\") as customer_firstname,
                ifnull(a.customer_middlename, \\"\\") as customer_middlename,
                ifnull(a.customer_lastname, \\"\\") as customer_lastname,
                ifnull(a.customer_suffix, \\"\\") as customer_suffix,
                ifnull(a.customer_dob, \\"\\") as customer_dob,
                ifnull(a.customer_note, \\"\\") as customer_note,
                a.customer_note_notify,
                a.customer_is_guest,
                ifnull(a.remote_ip, \\"\\") as remote_ip,
                ifnull(a.applied_rule_ids, \\"\\") as applied_rule_ids,
                ifnull(a.reserved_order_id, \\"\\") as reserved_order_id,
                ifnull(a.password_hash, \\"\\") as password_hash,
                ifnull(a.coupon_code, \\"\\") as coupon_code,
                ifnull(a.global_currency_code, \\"\\") as global_currency_code,
                ifnull(a.base_to_global_rate, \\"\\") as base_to_global_rate,
                ifnull(a.base_to_quote_rate, \\"\\") as base_to_quote_rate,
                ifnull(a.customer_taxvat, \\"\\") as customer_taxvat,
                ifnull(a.customer_gender, \\"\\") as customer_gender,
                ifnull(a.subtotal, \\"\\") as subtotal,
                ifnull(a.base_subtotal, \\"\\") as base_subtotal,
                ifnull(a.subtotal_with_discount, \\"\\") as subtotal_with_discount,
                ifnull(a.base_subtotal_with_discount, \\"\\") as base_subtotal_with_discount,
                ifnull(a.is_changed, \\"\\") as is_changed,
                a.trigger_recollect,
                ifnull(a.ext_shipping_info, \\"\\") as ext_shipping_info,
                ifnull(a.gift_message_id, \\"\\") as gift_message_id,
                a.is_persistent
            from quote a
                inner join store b on a.store_id = b.store_id
                inner join mapping_store c on b.code = c.code 
            order by a.entity_id
        ',
        'columns' => ['entity_id' => [PRIMARY], 'store_id' => [], 'created_at' => [], 'updated_at' => [], 'converted_at' => [NULLABLE], 'is_active' => [], 'is_virtual' => [], 'is_multi_shipping' => [], 'items_count' => [], 'items_qty' => [], 'orig_order_id' => [], 'store_to_base_rate' => [], 'store_to_quote_rate' => [], 'base_currency_code' => [NULLABLE], 'store_currency_code' => [NULLABLE], 'quote_currency_code' => [NULLABLE], 'grand_total' => [], 'base_grand_total' => [], 'checkout_method' => [NULLABLE], 'customer_id' => [NULLABLE], 'customer_tax_class_id' => [NULLABLE], 'customer_group_id' => [], 'customer_email' => [NULLABLE], 'customer_prefix' => [NULLABLE], 'customer_firstname' => [NULLABLE], 'customer_middlename' => [NULLABLE], 'customer_lastname' => [NULLABLE], 'customer_suffix' => [NULLABLE], 'customer_dob' => [NULLABLE], 'customer_note' => [NULLABLE], 'customer_note_notify' => [], 'customer_is_guest' => [], 'remote_ip' => [NULLABLE], 'applied_rule_ids' => [NULLABLE], 'reserved_order_id' => [NULLABLE], 'password_hash' => [NULLABLE], 'coupon_code' => [NULLABLE], 'global_currency_code' => [NULLABLE], 'base_to_global_rate' => [NULLABLE], 'base_to_quote_rate' => [NULLABLE], 'customer_taxvat' => [NULLABLE], 'customer_gender' => [NULLABLE], 'subtotal' => [NULLABLE], 'base_subtotal' => [NULLABLE], 'subtotal_with_discount' => [NULLABLE], 'base_subtotal_with_discount' => [], 'is_changed' => [NULLABLE], 'trigger_recollect' => [], 'ext_shipping_info' => [NULLABLE], 'gift_message_id' => [NULLABLE], 'is_persistent' => []],
    ],
    'quote_address' => [
        'export_query' => '
            select 
                a.address_id,
                a.quote_id,
                a.created_at,
                a.updated_at,
                ifnull(a.customer_id, \\"\\") as customer_id,
                ifnull(a.save_in_address_book, \\"\\") as save_in_address_book,
                ifnull(a.customer_address_id, \\"\\") as customer_address_id,
                ifnull(a.address_type, \\"\\") as address_type,
                ifnull(a.email, \\"\\") as email,
                ifnull(a.prefix, \\"\\") as prefix,
                ifnull(a.firstname, \\"\\") as firstname,
                ifnull(a.middlename, \\"\\") as middlename,
                ifnull(a.lastname, \\"\\") as lastname,
                ifnull(a.suffix, \\"\\") as suffix,
                ifnull(a.company, \\"\\") as company,
                ifnull(a.street, \\"\\") as street,
                ifnull(a.city, \\"\\") as city,
                ifnull(a.region, \\"\\") as region,
                ifnull(a.region_id, \\"\\") as region_id,
                ifnull(a.postcode, \\"\\") as postcode,
                ifnull(a.country_id, \\"\\") as country_id,
                ifnull(a.telephone, \\"\\") as telephone,
                ifnull(a.fax, \\"\\") as fax,
                a.same_as_billing,
                a.collect_shipping_rates,
                ifnull(a.shipping_method, \\"\\") as shipping_method,
                ifnull(a.shipping_description, \\"\\") as shipping_description,
                a.weight,
                a.subtotal,
                a.base_subtotal,
                a.subtotal_with_discount,
                a.base_subtotal_with_discount,
                a.tax_amount,
                a.base_tax_amount,
                a.shipping_amount,
                a.base_shipping_amount,
                ifnull(a.shipping_tax_amount, \\"\\") as shipping_tax_amount,
                ifnull(a.base_shipping_tax_amount, \\"\\") as base_shipping_tax_amount,
                a.discount_amount,
                a.base_discount_amount,
                a.grand_total,
                a.base_grand_total,
                ifnull(a.customer_notes, \\"\\") as customer_notes,
                ifnull(a.applied_taxes, \\"\\") as applied_taxes,
                ifnull(a.discount_description, \\"\\") as discount_description,
                ifnull(a.shipping_discount_amount, \\"\\") as shipping_discount_amount,
                ifnull(a.base_shipping_discount_amount, \\"\\") as base_shipping_discount_amount,
                ifnull(a.subtotal_incl_tax, \\"\\") as subtotal_incl_tax,
                ifnull(a.base_subtotal_total_incl_tax, \\"\\") as base_subtotal_total_incl_tax,
                ifnull(a.discount_tax_compensation_amount, \\"\\") as discount_tax_compensation_amount,
                ifnull(a.base_discount_tax_compensation_amount, \\"\\") as base_discount_tax_compensation_amount,
                ifnull(a.shipping_discount_tax_compensation_amount, \\"\\") as shipping_discount_tax_compensation_amount,
                ifnull(a.base_shipping_discount_tax_compensation_amnt, \\"\\") as base_shipping_discount_tax_compensation_amnt,
                ifnull(a.shipping_incl_tax, \\"\\") as shipping_incl_tax,
                ifnull(a.base_shipping_incl_tax, \\"\\") as base_shipping_incl_tax,
                ifnull(a.vat_id, \\"\\") as vat_id,
                ifnull(a.vat_is_valid, \\"\\") as vat_is_valid,
                ifnull(a.vat_request_id, \\"\\") as vat_request_id,
                ifnull(a.vat_request_date, \\"\\") as vat_request_date,
                ifnull(a.vat_request_success, \\"\\") as vat_request_success,
                ifnull(a.gift_message_id, \\"\\") as gift_message_id,
                ifnull(a.free_shipping, \\"\\") as free_shipping
            from quote_address a
                inner join mapping_quote b on a.quote_id = b.entity_id
        ',
        'columns' => ['address_id' => [PRIMARY], 'quote_id' => [], 'created_at' => [], 'updated_at' => [], 'customer_id' => [NULLABLE], 'save_in_address_book' => [NULLABLE], 'customer_address_id' => [NULLABLE], 'address_type' => [NULLABLE], 'email' => [NULLABLE], 'prefix' => [NULLABLE], 'firstname' => [NULLABLE], 'middlename' => [NULLABLE], 'lastname' => [NULLABLE], 'suffix' => [NULLABLE], 'company' => [NULLABLE], 'street' => [NULLABLE], 'city' => [NULLABLE], 'region' => [NULLABLE], 'region_id' => [NULLABLE], 'postcode' => [NULLABLE], 'country_id' => [NULLABLE], 'telephone' => [NULLABLE], 'fax' => [NULLABLE], 'same_as_billing' => [], 'collect_shipping_rates' => [], 'shipping_method' => [NULLABLE], 'shipping_description' => [NULLABLE], 'weight' => [], 'subtotal' => [], 'base_subtotal' => [], 'subtotal_with_discount' => [], 'base_subtotal_with_discount' => [], 'tax_amount' => [], 'base_tax_amount' => [], 'shipping_amount' => [], 'base_shipping_amount' => [], 'shipping_tax_amount' => [NULLABLE], 'base_shipping_tax_amount' => [NULLABLE], 'discount_amount' => [], 'base_discount_amount' => [], 'grand_total' => [], 'base_grand_total' => [], 'customer_notes' => [NULLABLE], 'applied_taxes' => [NULLABLE], 'discount_description' => [NULLABLE], 'shipping_discount_amount' => [NULLABLE], 'base_shipping_discount_amount' => [NULLABLE], 'subtotal_incl_tax' => [NULLABLE], 'base_subtotal_total_incl_tax' => [NULLABLE], 'discount_tax_compensation_amount' => [NULLABLE], 'base_discount_tax_compensation_amount' => [NULLABLE], 'shipping_discount_tax_compensation_amount' => [NULLABLE], 'base_shipping_discount_tax_compensation_amnt' => [NULLABLE], 'shipping_incl_tax' => [NULLABLE], 'base_shipping_incl_tax' => [NULLABLE], 'vat_id' => [NULLABLE], 'vat_is_valid' => [NULLABLE], 'vat_request_id' => [NULLABLE], 'vat_request_date' => [NULLABLE], 'vat_request_success' => [NULLABLE], 'gift_message_id' => [NULLABLE], 'free_shipping' => []],
    ],
    'quote_item' => [
        'export_query' => '
            select  
                a.item_id,
                a.quote_id,
                a.created_at,
                a.updated_at,
                ifnull(a.product_id, \\"\\") as product_id,
                ifnull(d.store_id, \\"\\") as store_id,
                ifnull(a.parent_item_id, \\"\\") as parent_item_id,
                ifnull(a.is_virtual, \\"\\") as is_virtual,
                ifnull(a.sku, \\"\\") as sku,
                ifnull(a.name, \\"\\") as name,
                ifnull(a.description, \\"\\") as description,
                ifnull(a.applied_rule_ids, \\"\\") as applied_rule_ids,
                ifnull(a.additional_data, \\"\\") as additional_data,
                ifnull(a.is_qty_decimal, \\"\\") as is_qty_decimal,
                ifnull(a.no_discount, \\"\\") as no_discount,
                ifnull(a.weight, \\"\\") as weight,
                a.qty,
                a.price,
                a.base_price,
                ifnull(a.custom_price, \\"\\") as custom_price,
                ifnull(a.discount_percent, \\"\\") as discount_percent,
                ifnull(a.discount_amount, \\"\\") as discount_amount,
                ifnull(a.base_discount_amount, \\"\\") as base_discount_amount,
                ifnull(a.tax_percent, \\"\\") as tax_percent,
                ifnull(a.tax_amount, \\"\\") as tax_amount,
                ifnull(a.base_tax_amount, \\"\\") as base_tax_amount,
                a.row_total,
                a.base_row_total,
                ifnull(a.row_total_with_discount, \\"\\") as row_total_with_discount,
                ifnull(a.row_weight, \\"\\") as row_weight,
                ifnull(a.product_type, \\"\\") as product_type,
                ifnull(a.base_tax_before_discount, \\"\\") as base_tax_before_discount,
                ifnull(a.tax_before_discount, \\"\\") as tax_before_discount,
                ifnull(a.original_custom_price, \\"\\") as original_custom_price,
                ifnull(a.redirect_url, \\"\\") as redirect_url,
                ifnull(a.base_cost, \\"\\") as base_cost,
                ifnull(a.price_incl_tax, \\"\\") as price_incl_tax,
                ifnull(a.base_price_incl_tax, \\"\\") as base_price_incl_tax,
                ifnull(a.row_total_incl_tax, \\"\\") as row_total_incl_tax,
                ifnull(a.base_row_total_incl_tax, \\"\\") as base_row_total_incl_tax,
                ifnull(a.discount_tax_compensation_amount, \\"\\") as discount_tax_compensation_amount,
                ifnull(a.base_discount_tax_compensation_amount, \\"\\") as base_discount_tax_compensation_amount,
                ifnull(a.gift_message_id, \\"\\") as gift_message_id,
                a.free_shipping,
                ifnull(a.weee_tax_applied, \\"\\") as weee_tax_applied,
                ifnull(a.weee_tax_applied_amount, \\"\\") as weee_tax_applied_amount,
                ifnull(a.weee_tax_applied_row_amount, \\"\\") as weee_tax_applied_row_amount,
                ifnull(a.weee_tax_disposition, \\"\\") as weee_tax_disposition,
                ifnull(a.weee_tax_row_disposition, \\"\\") as weee_tax_row_disposition,
                ifnull(a.base_weee_tax_applied_amount, \\"\\") as base_weee_tax_applied_amount,
                ifnull(a.base_weee_tax_applied_row_amnt, \\"\\") as base_weee_tax_applied_row_amnt,
                ifnull(a.base_weee_tax_disposition, \\"\\") as base_weee_tax_disposition,
                ifnull(a.base_weee_tax_row_disposition, \\"\\") as base_weee_tax_row_disposition
            from quote_item a 
                inner join mapping_quote b on a.quote_id = b.entity_id 
                inner join store c on a.store_id = c.store_id 
                inner join mapping_store d on c.code = d.code
            order by a.item_id 
        ',
        'columns' => ['item_id' => [PRIMARY], 'quote_id' => [], 'created_at' => [], 'updated_at' => [], 'product_id' => [NULLABLE], 'store_id' => [NULLABLE], 'parent_item_id' => [NULLABLE], 'is_virtual' => [NULLABLE], 'sku' => [NULLABLE], 'name' => [NULLABLE], 'description' => [NULLABLE], 'applied_rule_ids' => [NULLABLE], 'additional_data' => [NULLABLE], 'is_qty_decimal' => [NULLABLE], 'no_discount' => [NULLABLE], 'weight' => [NULLABLE], 'qty' => [], 'price' => [], 'base_price' => [], 'custom_price' => [NULLABLE], 'discount_percent' => [NULLABLE], 'discount_amount' => [NULLABLE], 'base_discount_amount' => [NULLABLE], 'tax_percent' => [NULLABLE], 'tax_amount' => [NULLABLE], 'base_tax_amount' => [NULLABLE], 'row_total' => [], 'base_row_total' => [], 'row_total_with_discount' => [NULLABLE], 'row_weight' => [NULLABLE], 'product_type' => [NULLABLE], 'base_tax_before_discount' => [NULLABLE], 'tax_before_discount' => [NULLABLE], 'original_custom_price' => [NULLABLE], 'redirect_url' => [NULLABLE], 'base_cost' => [NULLABLE], 'price_incl_tax' => [NULLABLE], 'base_price_incl_tax' => [NULLABLE], 'row_total_incl_tax' => [NULLABLE], 'base_row_total_incl_tax' => [NULLABLE], 'discount_tax_compensation_amount' => [NULLABLE], 'base_discount_tax_compensation_amount' => [NULLABLE], 'gift_message_id' => [NULLABLE], 'free_shipping' => [], 'weee_tax_applied' => [NULLABLE], 'weee_tax_applied_amount' => [NULLABLE], 'weee_tax_applied_row_amount' => [NULLABLE], 'weee_tax_disposition' => [NULLABLE], 'weee_tax_row_disposition' => [NULLABLE], 'base_weee_tax_applied_amount' => [NULLABLE], 'base_weee_tax_applied_row_amnt' => [NULLABLE], 'base_weee_tax_disposition' => [NULLABLE], 'base_weee_tax_row_disposition' => [NULLABLE]],
    ],
    'quote_id_mask' => [
        'export_query' => '
            select a.entity_id, a.quote_id, ifnull(a.masked_id, \\"\\") as masked_id 
            from quote_id_mask a 
                inner join mapping_quote b on a.quote_id = b.entity_id 
        ',
        'columns' => ['entity_id' => [PRIMARY],'quote_id' => [PRIMARY],'masked_id' => [NULLABLE]],
    ],
    'quote_item_option' => [
        'export_query' => '
        select a.option_id, a.item_id, a.product_id, a.code, ifnull(a.value, \\"\\") as value 
        from quote_item_option a
            inner join mapping_quote_item b on a.item_id = b.item_id
        ',
        'columns' => ['option_id' => [PRIMARY],'item_id' => [],'product_id' => [],'code' => [],'value' => [NULLABLE]],
    ],
    'quote_payment' => [
        'export_query' => '
            select a.payment_id, a.quote_id, a.created_at, a.updated_at, ifnull(a.method, \\"\\") as method, ifnull(a.cc_type, \\"\\") as cc_type, ifnull(a.cc_number_enc, \\"\\") as cc_number_enc, ifnull(a.cc_last_4, \\"\\") as cc_last_4, ifnull(a.cc_cid_enc, \\"\\") as cc_cid_enc, ifnull(a.cc_owner, \\"\\") as cc_owner, ifnull(a.cc_exp_month, \\"\\") as cc_exp_month, ifnull(a.cc_exp_year, \\"\\") as cc_exp_year, ifnull(a.cc_ss_owner, \\"\\") as cc_ss_owner, ifnull(a.cc_ss_start_month, \\"\\") as cc_ss_start_month, ifnull(a.cc_ss_start_year, \\"\\") as cc_ss_start_year, ifnull(a.po_number, \\"\\") as po_number, ifnull(a.additional_data, \\"\\") as additional_data, ifnull(a.cc_ss_issue, \\"\\") as cc_ss_issue, ifnull(a.additional_information, \\"\\") as additional_information, ifnull(a.paypal_payer_id, \\"\\") as paypal_payer_id, ifnull(a.paypal_payer_status, \\"\\") as paypal_payer_status, ifnull(a.paypal_correlation_id, \\"\\") as paypal_correlation_id 
            from quote_payment a 
                inner join mapping_quote b on a.quote_id = b.entity_id 
        ',
        'columns' => ['payment_id' => [PRIMARY],'quote_id' => [],'created_at' => [],'updated_at' => [],'method' => [NULLABLE],'cc_type' => [NULLABLE],'cc_number_enc' => [NULLABLE],'cc_last_4' => [NULLABLE],'cc_cid_enc' => [NULLABLE],'cc_owner' => [NULLABLE],'cc_exp_month' => [NULLABLE],'cc_exp_year' => [NULLABLE],'cc_ss_owner' => [NULLABLE],'cc_ss_start_month' => [NULLABLE],'cc_ss_start_year' => [NULLABLE],'po_number' => [NULLABLE],'additional_data' => [NULLABLE],'cc_ss_issue' => [NULLABLE],'additional_information' => [NULLABLE],'paypal_payer_id' => [NULLABLE],'paypal_payer_status' => [NULLABLE],'paypal_correlation_id' => [NULLABLE]],
    ],
    'quote_shipping_rate' => [
        'export_query' => '
            select a.rate_id, a.address_id, a.created_at, a.updated_at, ifnull(a.carrier, \\"\\") as carrier, ifnull(a.carrier_title, \\"\\") as carrier_title, ifnull(a.code, \\"\\") as code, ifnull(a.method, \\"\\") as method, ifnull(a.method_description, \\"\\") as method_description, a.price, ifnull(a.error_message, \\"\\") as error_message, ifnull(a.method_title, \\"\\") as method_title
            from quote_shipping_rate a 
                inner join mapping_quote_address b on a.address_id = b.address_id
        ',
        'columns' => ['rate_id' => [PRIMARY],'address_id' => [],'created_at' => [],'updated_at' => [],'carrier' => [NULLABLE],'carrier_title' => [NULLABLE],'code' => [NULLABLE],'method' => [NULLABLE],'method_description' => [NULLABLE],'price' => [],'error_message' => [NULLABLE],'method_title' => [NULLABLE]],
    ],
    // orders
    'sales_order' => [
        'export_query' => '
            select 
                a.entity_id, ifnull(a.state, \\"\\") as state, ifnull(a.status, \\"\\") as status, ifnull(a.coupon_code, \\"\\") as coupon_code, ifnull(a.protect_code, \\"\\") as protect_code, ifnull(a.shipping_description, \\"\\") as shipping_description, ifnull(a.is_virtual, \\"\\") as is_virtual, ifnull(c.store_id, \\"\\") as store_id, ifnull(d.entity_id, \\"\\") as customer_id, ifnull(a.base_discount_amount, \\"\\") as base_discount_amount, ifnull(a.base_discount_canceled, \\"\\") as base_discount_canceled, ifnull(a.base_discount_invoiced, \\"\\") as base_discount_invoiced, ifnull(a.base_discount_refunded, \\"\\") as base_discount_refunded, ifnull(a.base_grand_total, \\"\\") as base_grand_total, ifnull(a.base_shipping_amount, \\"\\") as base_shipping_amount, ifnull(a.base_shipping_canceled, \\"\\") as base_shipping_canceled, ifnull(a.base_shipping_invoiced, \\"\\") as base_shipping_invoiced, ifnull(a.base_shipping_refunded, \\"\\") as base_shipping_refunded, ifnull(a.base_shipping_tax_amount, \\"\\") as base_shipping_tax_amount, ifnull(a.base_shipping_tax_refunded, \\"\\") as base_shipping_tax_refunded, ifnull(a.base_subtotal, \\"\\") as base_subtotal, ifnull(a.base_subtotal_canceled, \\"\\") as base_subtotal_canceled, ifnull(a.base_subtotal_invoiced, \\"\\") as base_subtotal_invoiced, ifnull(a.base_subtotal_refunded, \\"\\") as base_subtotal_refunded, ifnull(a.base_tax_amount, \\"\\") as base_tax_amount, ifnull(a.base_tax_canceled, \\"\\") as base_tax_canceled, ifnull(a.base_tax_invoiced, \\"\\") as base_tax_invoiced, ifnull(a.base_tax_refunded, \\"\\") as base_tax_refunded, ifnull(a.base_to_global_rate, \\"\\") as base_to_global_rate, ifnull(a.base_to_order_rate, \\"\\") as base_to_order_rate, ifnull(a.base_total_canceled, \\"\\") as base_total_canceled, ifnull(a.base_total_invoiced, \\"\\") as base_total_invoiced, ifnull(a.base_total_invoiced_cost, \\"\\") as base_total_invoiced_cost, ifnull(a.base_total_offline_refunded, \\"\\") as base_total_offline_refunded, ifnull(a.base_total_online_refunded, \\"\\") as base_total_online_refunded, ifnull(a.base_total_paid, \\"\\") as base_total_paid, ifnull(a.base_total_qty_ordered, \\"\\") as base_total_qty_ordered, ifnull(a.base_total_refunded, \\"\\") as base_total_refunded, ifnull(a.discount_amount, \\"\\") as discount_amount, ifnull(a.discount_canceled, \\"\\") as discount_canceled, ifnull(a.discount_invoiced, \\"\\") as discount_invoiced, ifnull(a.discount_refunded, \\"\\") as discount_refunded, ifnull(a.grand_total, \\"\\") as grand_total, ifnull(a.shipping_amount, \\"\\") as shipping_amount, ifnull(a.shipping_canceled, \\"\\") as shipping_canceled, ifnull(a.shipping_invoiced, \\"\\") as shipping_invoiced, ifnull(a.shipping_refunded, \\"\\") as shipping_refunded, ifnull(a.shipping_tax_amount, \\"\\") as shipping_tax_amount, ifnull(a.shipping_tax_refunded, \\"\\") as shipping_tax_refunded, ifnull(a.store_to_base_rate, \\"\\") as store_to_base_rate, ifnull(a.store_to_order_rate, \\"\\") as store_to_order_rate, ifnull(a.subtotal, \\"\\") as subtotal, ifnull(a.subtotal_canceled, \\"\\") as subtotal_canceled, ifnull(a.subtotal_invoiced, \\"\\") as subtotal_invoiced, ifnull(a.subtotal_refunded, \\"\\") as subtotal_refunded, ifnull(a.tax_amount, \\"\\") as tax_amount, ifnull(a.tax_canceled, \\"\\") as tax_canceled, ifnull(a.tax_invoiced, \\"\\") as tax_invoiced, ifnull(a.tax_refunded, \\"\\") as tax_refunded, ifnull(a.total_canceled, \\"\\") as total_canceled, ifnull(a.total_invoiced, \\"\\") as total_invoiced, ifnull(a.total_offline_refunded, \\"\\") as total_offline_refunded, ifnull(a.total_online_refunded, \\"\\") as total_online_refunded, ifnull(a.total_paid, \\"\\") as total_paid, ifnull(a.total_qty_ordered, \\"\\") as total_qty_ordered, ifnull(a.total_refunded, \\"\\") as total_refunded, ifnull(a.can_ship_partially, \\"\\") as can_ship_partially, ifnull(a.can_ship_partially_item, \\"\\") as can_ship_partially_item, ifnull(a.customer_is_guest, \\"\\") as customer_is_guest, ifnull(a.customer_note_notify, \\"\\") as customer_note_notify, ifnull(a.billing_address_id, \\"\\") as billing_address_id, ifnull(a.customer_group_id, \\"\\") as customer_group_id, ifnull(a.edit_increment, \\"\\") as edit_increment, ifnull(a.email_sent, \\"\\") as email_sent, ifnull(a.send_email, \\"\\") as send_email, ifnull(a.forced_shipment_with_invoice, \\"\\") as forced_shipment_with_invoice, ifnull(a.payment_auth_expiration, \\"\\") as payment_auth_expiration, ifnull(a.quote_address_id, \\"\\") as quote_address_id, ifnull(a.quote_id, \\"\\") as quote_id, ifnull(a.shipping_address_id, \\"\\") as shipping_address_id, ifnull(a.adjustment_negative, \\"\\") as adjustment_negative, ifnull(a.adjustment_positive, \\"\\") as adjustment_positive, ifnull(a.base_adjustment_negative, \\"\\") as base_adjustment_negative, ifnull(a.base_adjustment_positive, \\"\\") as base_adjustment_positive, ifnull(a.base_shipping_discount_amount, \\"\\") as base_shipping_discount_amount, ifnull(a.base_subtotal_incl_tax, \\"\\") as base_subtotal_incl_tax, ifnull(a.base_total_due, \\"\\") as base_total_due, ifnull(a.payment_authorization_amount, \\"\\") as payment_authorization_amount, ifnull(a.shipping_discount_amount, \\"\\") as shipping_discount_amount, ifnull(a.subtotal_incl_tax, \\"\\") as subtotal_incl_tax, ifnull(a.total_due, \\"\\") as total_due, ifnull(a.weight, \\"\\") as weight, ifnull(a.customer_dob, \\"\\") as customer_dob, ifnull(a.increment_id, \\"\\") as increment_id, ifnull(a.applied_rule_ids, \\"\\") as applied_rule_ids, ifnull(a.base_currency_code, \\"\\") as base_currency_code, ifnull(a.customer_email, \\"\\") as customer_email, ifnull(a.customer_firstname, \\"\\") as customer_firstname, ifnull(a.customer_lastname, \\"\\") as customer_lastname, ifnull(a.customer_middlename, \\"\\") as customer_middlename, ifnull(a.customer_prefix, \\"\\") as customer_prefix, ifnull(a.customer_suffix, \\"\\") as customer_suffix, ifnull(a.customer_taxvat, \\"\\") as customer_taxvat, ifnull(a.discount_description, \\"\\") as discount_description, ifnull(a.ext_customer_id, \\"\\") as ext_customer_id, ifnull(a.ext_order_id, \\"\\") as ext_order_id, ifnull(a.global_currency_code, \\"\\") as global_currency_code, ifnull(a.hold_before_state, \\"\\") as hold_before_state, ifnull(a.hold_before_status, \\"\\") as hold_before_status, ifnull(a.order_currency_code, \\"\\") as order_currency_code, ifnull(a.original_increment_id, \\"\\") as original_increment_id, ifnull(a.relation_child_id, \\"\\") as relation_child_id, ifnull(a.relation_child_real_id, \\"\\") as relation_child_real_id, ifnull(a.relation_parent_id, \\"\\") as relation_parent_id, ifnull(a.relation_parent_real_id, \\"\\") as relation_parent_real_id, ifnull(a.remote_ip, \\"\\") as remote_ip, ifnull(a.shipping_method, \\"\\") as shipping_method, ifnull(a.store_currency_code, \\"\\") as store_currency_code, ifnull(a.store_name, \\"\\") as store_name, ifnull(a.x_forwarded_for, \\"\\") as x_forwarded_for, ifnull(a.customer_note, \\"\\") as customer_note, a.created_at, a.updated_at, a.total_item_count, ifnull(a.customer_gender, \\"\\") as customer_gender, ifnull(a.discount_tax_compensation_amount, \\"\\") as discount_tax_compensation_amount, ifnull(a.base_discount_tax_compensation_amount, \\"\\") as base_discount_tax_compensation_amount, ifnull(a.shipping_discount_tax_compensation_amount, \\"\\") as shipping_discount_tax_compensation_amount, ifnull(a.base_shipping_discount_tax_compensation_amnt, \\"\\") as base_shipping_discount_tax_compensation_amnt, ifnull(a.discount_tax_compensation_invoiced, \\"\\") as discount_tax_compensation_invoiced, ifnull(a.base_discount_tax_compensation_invoiced, \\"\\") as base_discount_tax_compensation_invoiced, ifnull(a.discount_tax_compensation_refunded, \\"\\") as discount_tax_compensation_refunded, ifnull(a.base_discount_tax_compensation_refunded, \\"\\") as base_discount_tax_compensation_refunded, ifnull(a.shipping_incl_tax, \\"\\") as shipping_incl_tax, ifnull(a.base_shipping_incl_tax, \\"\\") as base_shipping_incl_tax, ifnull(a.coupon_rule_name, \\"\\") as coupon_rule_name, ifnull(a.gift_message_id, \\"\\") as gift_message_id, ifnull(a.paypal_ipn_customer_notified, \\"\\") as paypal_ipn_customer_notified, ifnull(a.udropship_shipping_details, \\"\\") as udropship_shipping_details, ifnull(a.udropship_status, \\"\\") as udropship_status, ifnull(a.ud_amount_fields, \\"\\") as ud_amount_fields
            from sales_order a
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code 
                left join mapping_customer_entity d on a.customer_id = d.entity_id 
        ',
        'columns' => ['entity_id' => [PRIMARY],'state' => [NULLABLE],'status' => [NULLABLE],'coupon_code' => [NULLABLE],'protect_code' => [NULLABLE],'shipping_description' => [NULLABLE],'is_virtual' => [NULLABLE],'store_id' => [NULLABLE],'customer_id' => [NULLABLE],'base_discount_amount' => [NULLABLE],'base_discount_canceled' => [NULLABLE],'base_discount_invoiced' => [NULLABLE],'base_discount_refunded' => [NULLABLE],'base_grand_total' => [NULLABLE],'base_shipping_amount' => [NULLABLE],'base_shipping_canceled' => [NULLABLE],'base_shipping_invoiced' => [NULLABLE],'base_shipping_refunded' => [NULLABLE],'base_shipping_tax_amount' => [NULLABLE],'base_shipping_tax_refunded' => [NULLABLE],'base_subtotal' => [NULLABLE],'base_subtotal_canceled' => [NULLABLE],'base_subtotal_invoiced' => [NULLABLE],'base_subtotal_refunded' => [NULLABLE],'base_tax_amount' => [NULLABLE],'base_tax_canceled' => [NULLABLE],'base_tax_invoiced' => [NULLABLE],'base_tax_refunded' => [NULLABLE],'base_to_global_rate' => [NULLABLE],'base_to_order_rate' => [NULLABLE],'base_total_canceled' => [NULLABLE],'base_total_invoiced' => [NULLABLE],'base_total_invoiced_cost' => [NULLABLE],'base_total_offline_refunded' => [NULLABLE],'base_total_online_refunded' => [NULLABLE],'base_total_paid' => [NULLABLE],'base_total_qty_ordered' => [NULLABLE],'base_total_refunded' => [NULLABLE],'discount_amount' => [NULLABLE],'discount_canceled' => [NULLABLE],'discount_invoiced' => [NULLABLE],'discount_refunded' => [NULLABLE],'grand_total' => [NULLABLE],'shipping_amount' => [NULLABLE],'shipping_canceled' => [NULLABLE],'shipping_invoiced' => [NULLABLE],'shipping_refunded' => [NULLABLE],'shipping_tax_amount' => [NULLABLE],'shipping_tax_refunded' => [NULLABLE],'store_to_base_rate' => [NULLABLE],'store_to_order_rate' => [NULLABLE],'subtotal' => [NULLABLE],'subtotal_canceled' => [NULLABLE],'subtotal_invoiced' => [NULLABLE],'subtotal_refunded' => [NULLABLE],'tax_amount' => [NULLABLE],'tax_canceled' => [NULLABLE],'tax_invoiced' => [NULLABLE],'tax_refunded' => [NULLABLE],'total_canceled' => [NULLABLE],'total_invoiced' => [NULLABLE],'total_offline_refunded' => [NULLABLE],'total_online_refunded' => [NULLABLE],'total_paid' => [NULLABLE],'total_qty_ordered' => [NULLABLE],'total_refunded' => [NULLABLE],'can_ship_partially' => [NULLABLE],'can_ship_partially_item' => [NULLABLE],'customer_is_guest' => [NULLABLE],'customer_note_notify' => [NULLABLE],'billing_address_id' => [NULLABLE],'customer_group_id' => [NULLABLE],'edit_increment' => [NULLABLE],'email_sent' => [NULLABLE],'send_email' => [NULLABLE],'forced_shipment_with_invoice' => [NULLABLE],'payment_auth_expiration' => [NULLABLE],'quote_address_id' => [NULLABLE],'quote_id' => [NULLABLE],'shipping_address_id' => [NULLABLE],'adjustment_negative' => [NULLABLE],'adjustment_positive' => [NULLABLE],'base_adjustment_negative' => [NULLABLE],'base_adjustment_positive' => [NULLABLE],'base_shipping_discount_amount' => [NULLABLE],'base_subtotal_incl_tax' => [NULLABLE],'base_total_due' => [NULLABLE],'payment_authorization_amount' => [NULLABLE],'shipping_discount_amount' => [NULLABLE],'subtotal_incl_tax' => [NULLABLE],'total_due' => [NULLABLE],'weight' => [NULLABLE],'customer_dob' => [NULLABLE],'increment_id' => [NULLABLE],'applied_rule_ids' => [NULLABLE],'base_currency_code' => [NULLABLE],'customer_email' => [NULLABLE],'customer_firstname' => [NULLABLE],'customer_lastname' => [NULLABLE],'customer_middlename' => [NULLABLE],'customer_prefix' => [NULLABLE],'customer_suffix' => [NULLABLE],'customer_taxvat' => [NULLABLE],'discount_description' => [NULLABLE],'ext_customer_id' => [NULLABLE],'ext_order_id' => [NULLABLE],'global_currency_code' => [NULLABLE],'hold_before_state' => [NULLABLE],'hold_before_status' => [NULLABLE],'order_currency_code' => [NULLABLE],'original_increment_id' => [NULLABLE],'relation_child_id' => [NULLABLE],'relation_child_real_id' => [NULLABLE],'relation_parent_id' => [NULLABLE],'relation_parent_real_id' => [NULLABLE],'remote_ip' => [NULLABLE],'shipping_method' => [NULLABLE],'store_currency_code' => [NULLABLE],'store_name' => [NULLABLE],'x_forwarded_for' => [NULLABLE],'customer_note' => [NULLABLE],'created_at' => [],'updated_at' => [],'total_item_count' => [],'customer_gender' => [NULLABLE],'discount_tax_compensation_amount' => [NULLABLE],'base_discount_tax_compensation_amount' => [NULLABLE],'shipping_discount_tax_compensation_amount' => [NULLABLE],'base_shipping_discount_tax_compensation_amnt' => [NULLABLE],'discount_tax_compensation_invoiced' => [NULLABLE],'base_discount_tax_compensation_invoiced' => [NULLABLE],'discount_tax_compensation_refunded' => [NULLABLE],'base_discount_tax_compensation_refunded' => [NULLABLE],'shipping_incl_tax' => [NULLABLE],'base_shipping_incl_tax' => [NULLABLE],'coupon_rule_name' => [NULLABLE],'gift_message_id' => [NULLABLE],'paypal_ipn_customer_notified' => [NULLABLE],'udropship_shipping_details' => [NULLABLE],'udropship_status' => [NULLABLE],'ud_amount_fields' => [NULLABLE]],
    ],
    'sales_order_address' => [
        'export_query' => '
            select 
                a.entity_id, ifnull(a.parent_id, \\"\\") as parent_id, ifnull(a.customer_address_id, \\"\\") as customer_address_id, ifnull(a.quote_address_id, \\"\\") as quote_address_id, ifnull(a.region_id, \\"\\") as region_id, ifnull(a.customer_id, \\"\\") as customer_id, ifnull(a.fax, \\"\\") as fax, ifnull(a.region, \\"\\") as region, ifnull(a.postcode, \\"\\") as postcode, ifnull(a.lastname, \\"\\") as lastname, ifnull(a.street, \\"\\") as street, ifnull(a.city, \\"\\") as city, ifnull(a.email, \\"\\") as email, ifnull(a.telephone, \\"\\") as telephone, ifnull(a.country_id, \\"\\") as country_id, ifnull(a.firstname, \\"\\") as firstname, ifnull(a.address_type, \\"\\") as address_type, ifnull(a.prefix, \\"\\") as prefix, ifnull(a.middlename, \\"\\") as middlename, ifnull(a.suffix, \\"\\") as suffix, ifnull(a.company, \\"\\") as company, ifnull(a.vat_id, \\"\\") as vat_id, ifnull(a.vat_is_valid, \\"\\") as vat_is_valid, ifnull(a.vat_request_id, \\"\\") as vat_request_id, ifnull(a.vat_request_date, \\"\\") as vat_request_date, ifnull(a.vat_request_success, \\"\\") as vat_request_success
            from sales_order_address a 
        ',
        'columns' => ['entity_id' => [PRIMARY],'parent_id' => [NULLABLE],'customer_address_id' => [NULLABLE],'quote_address_id' => [NULLABLE],'region_id' => [NULLABLE],'customer_id' => [NULLABLE],'fax' => [NULLABLE],'region' => [NULLABLE],'postcode' => [NULLABLE],'lastname' => [NULLABLE],'street' => [NULLABLE],'city' => [NULLABLE],'email' => [NULLABLE],'telephone' => [NULLABLE],'country_id' => [NULLABLE],'firstname' => [NULLABLE],'address_type' => [NULLABLE],'prefix' => [NULLABLE],'middlename' => [NULLABLE],'suffix' => [NULLABLE],'company' => [NULLABLE],'vat_id' => [NULLABLE],'vat_is_valid' => [NULLABLE],'vat_request_id' => [NULLABLE],'vat_request_date' => [NULLABLE],'vat_request_success' => [NULLABLE]],
    ],
    'sales_order_grid' => [
        'export_query' => '
            select 
                a.entity_id, ifnull(a.status, \\"\\") as status, ifnull(c.store_id, \\"\\") as store_id, ifnull(a.store_name, \\"\\") as store_name, ifnull(d.entity_id, \\"\\") as customer_id, ifnull(a.base_grand_total, \\"\\") as base_grand_total, ifnull(a.base_total_paid, \\"\\") as base_total_paid, ifnull(a.grand_total, \\"\\") as grand_total, ifnull(a.total_paid, \\"\\") as total_paid, ifnull(a.increment_id, \\"\\") as increment_id, ifnull(a.base_currency_code, \\"\\") as base_currency_code, ifnull(a.order_currency_code, \\"\\") as order_currency_code, ifnull(a.shipping_name, \\"\\") as shipping_name, ifnull(a.billing_name, \\"\\") as billing_name, ifnull(a.created_at, \\"\\") as created_at, ifnull(a.updated_at, \\"\\") as updated_at, ifnull(a.billing_address, \\"\\") as billing_address, ifnull(a.shipping_address, \\"\\") as shipping_address, ifnull(a.shipping_information, \\"\\") as shipping_information, ifnull(a.customer_email, \\"\\") as customer_email, ifnull(a.customer_group, \\"\\") as customer_group, ifnull(a.subtotal, \\"\\") as subtotal, ifnull(a.shipping_and_handling, \\"\\") as shipping_and_handling, ifnull(a.customer_name, \\"\\") as customer_name, ifnull(a.payment_method, \\"\\") as payment_method, ifnull(a.total_refunded, \\"\\") as total_refunded
            from sales_order_grid a 
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code 
                left join mapping_customer_entity d on a.customer_id = d.entity_id 
        ',
        'columns' => ['entity_id' => [PRIMARY],'status' => [NULLABLE],'store_id' => [NULLABLE, UNIQUE_KEY],'store_name' => [NULLABLE],'customer_id' => [NULLABLE],'base_grand_total' => [NULLABLE],'base_total_paid' => [NULLABLE],'grand_total' => [NULLABLE],'total_paid' => [NULLABLE],'increment_id' => [NULLABLE, UNIQUE_KEY],'base_currency_code' => [NULLABLE],'order_currency_code' => [NULLABLE],'shipping_name' => [NULLABLE],'billing_name' => [NULLABLE],'created_at' => [NULLABLE],'updated_at' => [NULLABLE],'billing_address' => [NULLABLE],'shipping_address' => [NULLABLE],'shipping_information' => [NULLABLE],'customer_email' => [NULLABLE],'customer_group' => [NULLABLE],'subtotal' => [NULLABLE],'shipping_and_handling' => [NULLABLE],'customer_name' => [NULLABLE],'payment_method' => [NULLABLE],'total_refunded' => [NULLABLE]],
    ], 
    'sales_order_item' => [
        'export_query' => '
            select 
                a.item_id, a.order_id, ifnull(a.parent_item_id, \\"\\") as parent_item_id, ifnull(a.quote_item_id, \\"\\") as quote_item_id, ifnull(c.store_id, \\"\\") as store_id, a.created_at, a.updated_at, ifnull(a.product_id, \\"\\") as product_id, ifnull(a.product_type, \\"\\") as product_type, ifnull(a.product_options, \\"\\") as product_options, ifnull(a.weight, \\"\\") as weight, ifnull(a.is_virtual, \\"\\") as is_virtual, ifnull(a.sku, \\"\\") as sku, ifnull(a.name, \\"\\") as name, ifnull(a.description, \\"\\") as description, ifnull(a.applied_rule_ids, \\"\\") as applied_rule_ids, ifnull(a.additional_data, \\"\\") as additional_data, ifnull(a.is_qty_decimal, \\"\\") as is_qty_decimal, a.no_discount, ifnull(a.qty_backordered, \\"\\") as qty_backordered, ifnull(a.qty_canceled, \\"\\") as qty_canceled, ifnull(a.qty_invoiced, \\"\\") as qty_invoiced, ifnull(a.qty_ordered, \\"\\") as qty_ordered, ifnull(a.qty_refunded, \\"\\") as qty_refunded, ifnull(a.qty_shipped, \\"\\") as qty_shipped, ifnull(a.base_cost, \\"\\") as base_cost, a.price, a.base_price, ifnull(a.original_price, \\"\\") as original_price, ifnull(a.base_original_price, \\"\\") as base_original_price, ifnull(a.tax_percent, \\"\\") as tax_percent, ifnull(a.tax_amount, \\"\\") as tax_amount, ifnull(a.base_tax_amount, \\"\\") as base_tax_amount, ifnull(a.tax_invoiced, \\"\\") as tax_invoiced, ifnull(a.base_tax_invoiced, \\"\\") as base_tax_invoiced, ifnull(a.discount_percent, \\"\\") as discount_percent, ifnull(a.discount_amount, \\"\\") as discount_amount, ifnull(a.base_discount_amount, \\"\\") as base_discount_amount, ifnull(a.discount_invoiced, \\"\\") as discount_invoiced, ifnull(a.base_discount_invoiced, \\"\\") as base_discount_invoiced, ifnull(a.amount_refunded, \\"\\") as amount_refunded, ifnull(a.base_amount_refunded, \\"\\") as base_amount_refunded, a.row_total, a.base_row_total, a.row_invoiced, a.base_row_invoiced, ifnull(a.row_weight, \\"\\") as row_weight, ifnull(a.base_tax_before_discount, \\"\\") as base_tax_before_discount, ifnull(a.tax_before_discount, \\"\\") as tax_before_discount, ifnull(a.ext_order_item_id, \\"\\") as ext_order_item_id, ifnull(a.locked_do_invoice, \\"\\") as locked_do_invoice, ifnull(a.locked_do_ship, \\"\\") as locked_do_ship, ifnull(a.price_incl_tax, \\"\\") as price_incl_tax, ifnull(a.base_price_incl_tax, \\"\\") as base_price_incl_tax, ifnull(a.row_total_incl_tax, \\"\\") as row_total_incl_tax, ifnull(a.base_row_total_incl_tax, \\"\\") as base_row_total_incl_tax, ifnull(a.discount_tax_compensation_amount, \\"\\") as discount_tax_compensation_amount, ifnull(a.base_discount_tax_compensation_amount, \\"\\") as base_discount_tax_compensation_amount, ifnull(a.discount_tax_compensation_invoiced, \\"\\") as discount_tax_compensation_invoiced, ifnull(a.base_discount_tax_compensation_invoiced, \\"\\") as base_discount_tax_compensation_invoiced, ifnull(a.discount_tax_compensation_refunded, \\"\\") as discount_tax_compensation_refunded, ifnull(a.base_discount_tax_compensation_refunded, \\"\\") as base_discount_tax_compensation_refunded, ifnull(a.tax_canceled, \\"\\") as tax_canceled, ifnull(a.discount_tax_compensation_canceled, \\"\\") as discount_tax_compensation_canceled, ifnull(a.tax_refunded, \\"\\") as tax_refunded, ifnull(a.base_tax_refunded, \\"\\") as base_tax_refunded, ifnull(a.discount_refunded, \\"\\") as discount_refunded, ifnull(a.base_discount_refunded, \\"\\") as base_discount_refunded, a.free_shipping, ifnull(a.gift_message_id, \\"\\") as gift_message_id, ifnull(a.gift_message_available, \\"\\") as gift_message_available, ifnull(a.weee_tax_applied, \\"\\") as weee_tax_applied, ifnull(a.weee_tax_applied_amount, \\"\\") as weee_tax_applied_amount, ifnull(a.weee_tax_applied_row_amount, \\"\\") as weee_tax_applied_row_amount, ifnull(a.weee_tax_disposition, \\"\\") as weee_tax_disposition, ifnull(a.weee_tax_row_disposition, \\"\\") as weee_tax_row_disposition, ifnull(a.base_weee_tax_applied_amount, \\"\\") as base_weee_tax_applied_amount, ifnull(a.base_weee_tax_applied_row_amnt, \\"\\") as base_weee_tax_applied_row_amnt, ifnull(a.base_weee_tax_disposition, \\"\\") as base_weee_tax_disposition, ifnull(a.base_weee_tax_row_disposition, \\"\\") as base_weee_tax_row_disposition, ifnull(a.udropship_vendor, \\"\\") as udropship_vendor, ifnull(a.ud_base_tax_amount, \\"\\") as ud_base_tax_amount, ifnull(a.ud_base_hidden_tax_amount, \\"\\") as ud_base_hidden_tax_amount, ifnull(a.ud_base_discount_amount, \\"\\") as ud_base_discount_amount, ifnull(a.ud_base_row_total, \\"\\") as ud_base_row_total, ifnull(a.ud_row_total, \\"\\") as ud_row_total
            from sales_order_item a
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code 
        ',
        'columns' => ['item_id' => [PRIMARY],'order_id' => [],'parent_item_id' => [NULLABLE],'quote_item_id' => [NULLABLE],'store_id' => [NULLABLE],'created_at' => [],'updated_at' => [],'product_id' => [NULLABLE],'product_type' => [NULLABLE],'product_options' => [NULLABLE],'weight' => [NULLABLE],'is_virtual' => [NULLABLE],'sku' => [NULLABLE],'name' => [NULLABLE],'description' => [NULLABLE],'applied_rule_ids' => [NULLABLE],'additional_data' => [NULLABLE],'is_qty_decimal' => [NULLABLE],'no_discount' => [],'qty_backordered' => [NULLABLE],'qty_canceled' => [NULLABLE],'qty_invoiced' => [NULLABLE],'qty_ordered' => [NULLABLE],'qty_refunded' => [NULLABLE],'qty_shipped' => [NULLABLE],'base_cost' => [NULLABLE],'price' => [],'base_price' => [],'original_price' => [NULLABLE],'base_original_price' => [NULLABLE],'tax_percent' => [NULLABLE],'tax_amount' => [NULLABLE],'base_tax_amount' => [NULLABLE],'tax_invoiced' => [NULLABLE],'base_tax_invoiced' => [NULLABLE],'discount_percent' => [NULLABLE],'discount_amount' => [NULLABLE],'base_discount_amount' => [NULLABLE],'discount_invoiced' => [NULLABLE],'base_discount_invoiced' => [NULLABLE],'amount_refunded' => [NULLABLE],'base_amount_refunded' => [NULLABLE],'row_total' => [],'base_row_total' => [],'row_invoiced' => [],'base_row_invoiced' => [],'row_weight' => [NULLABLE],'base_tax_before_discount' => [NULLABLE],'tax_before_discount' => [NULLABLE],'ext_order_item_id' => [NULLABLE],'locked_do_invoice' => [NULLABLE],'locked_do_ship' => [NULLABLE],'price_incl_tax' => [NULLABLE],'base_price_incl_tax' => [NULLABLE],'row_total_incl_tax' => [NULLABLE],'base_row_total_incl_tax' => [NULLABLE],'discount_tax_compensation_amount' => [NULLABLE],'base_discount_tax_compensation_amount' => [NULLABLE],'discount_tax_compensation_invoiced' => [NULLABLE],'base_discount_tax_compensation_invoiced' => [NULLABLE],'discount_tax_compensation_refunded' => [NULLABLE],'base_discount_tax_compensation_refunded' => [NULLABLE],'tax_canceled' => [NULLABLE],'discount_tax_compensation_canceled' => [NULLABLE],'tax_refunded' => [NULLABLE],'base_tax_refunded' => [NULLABLE],'discount_refunded' => [NULLABLE],'base_discount_refunded' => [NULLABLE],'free_shipping' => [],'gift_message_id' => [NULLABLE],'gift_message_available' => [NULLABLE],'weee_tax_applied' => [NULLABLE],'weee_tax_applied_amount' => [NULLABLE],'weee_tax_applied_row_amount' => [NULLABLE],'weee_tax_disposition' => [NULLABLE],'weee_tax_row_disposition' => [NULLABLE],'base_weee_tax_applied_amount' => [NULLABLE],'base_weee_tax_applied_row_amnt' => [NULLABLE],'base_weee_tax_disposition' => [NULLABLE],'base_weee_tax_row_disposition' => [NULLABLE],'udropship_vendor' => [NULLABLE],'ud_base_tax_amount' => [NULLABLE],'ud_base_hidden_tax_amount' => [NULLABLE],'ud_base_discount_amount' => [NULLABLE],'ud_base_row_total' => [NULLABLE],'ud_row_total' => [NULLABLE]],
    ],
    'sales_order_payment' => [
        'export_query' => '
            select 
                a.entity_id, a.parent_id, ifnull(a.base_shipping_captured, \\"\\") as base_shipping_captured, ifnull(a.shipping_captured, \\"\\") as shipping_captured, ifnull(a.amount_refunded, \\"\\") as amount_refunded, ifnull(a.base_amount_paid, \\"\\") as base_amount_paid, ifnull(a.amount_canceled, \\"\\") as amount_canceled, ifnull(a.base_amount_authorized, \\"\\") as base_amount_authorized, ifnull(a.base_amount_paid_online, \\"\\") as base_amount_paid_online, ifnull(a.base_amount_refunded_online, \\"\\") as base_amount_refunded_online, ifnull(a.base_shipping_amount, \\"\\") as base_shipping_amount, ifnull(a.shipping_amount, \\"\\") as shipping_amount, ifnull(a.amount_paid, \\"\\") as amount_paid, ifnull(a.amount_authorized, \\"\\") as amount_authorized, ifnull(a.base_amount_ordered, \\"\\") as base_amount_ordered, ifnull(a.base_shipping_refunded, \\"\\") as base_shipping_refunded, ifnull(a.shipping_refunded, \\"\\") as shipping_refunded, ifnull(a.base_amount_refunded, \\"\\") as base_amount_refunded, ifnull(a.amount_ordered, \\"\\") as amount_ordered, ifnull(a.base_amount_canceled, \\"\\") as base_amount_canceled, ifnull(a.quote_payment_id, \\"\\") as quote_payment_id, ifnull(a.additional_data, \\"\\") as additional_data, ifnull(a.cc_exp_month, \\"\\") as cc_exp_month, ifnull(a.cc_ss_start_year, \\"\\") as cc_ss_start_year, ifnull(a.echeck_bank_name, \\"\\") as echeck_bank_name, ifnull(a.method, \\"\\") as method, ifnull(a.cc_debug_request_body, \\"\\") as cc_debug_request_body, ifnull(a.cc_secure_verify, \\"\\") as cc_secure_verify, ifnull(a.protection_eligibility, \\"\\") as protection_eligibility, ifnull(a.cc_approval, \\"\\") as cc_approval, ifnull(a.cc_last_4, \\"\\") as cc_last_4, ifnull(a.cc_status_description, \\"\\") as cc_status_description, ifnull(a.echeck_type, \\"\\") as echeck_type, ifnull(a.cc_debug_response_serialized, \\"\\") as cc_debug_response_serialized, ifnull(a.cc_ss_start_month, \\"\\") as cc_ss_start_month, ifnull(a.echeck_account_type, \\"\\") as echeck_account_type, ifnull(a.last_trans_id, \\"\\") as last_trans_id, ifnull(a.cc_cid_status, \\"\\") as cc_cid_status, ifnull(a.cc_owner, \\"\\") as cc_owner, ifnull(a.cc_type, \\"\\") as cc_type, ifnull(a.po_number, \\"\\") as po_number, ifnull(a.cc_exp_year, \\"\\") as cc_exp_year, ifnull(a.cc_status, \\"\\") as cc_status, ifnull(a.echeck_routing_number, \\"\\") as echeck_routing_number, ifnull(a.account_status, \\"\\") as account_status, ifnull(a.anet_trans_method, \\"\\") as anet_trans_method, ifnull(a.cc_debug_response_body, \\"\\") as cc_debug_response_body, ifnull(a.cc_ss_issue, \\"\\") as cc_ss_issue, ifnull(a.echeck_account_name, \\"\\") as echeck_account_name, ifnull(a.cc_avs_status, \\"\\") as cc_avs_status, ifnull(a.cc_number_enc, \\"\\") as cc_number_enc, ifnull(a.cc_trans_id, \\"\\") as cc_trans_id, ifnull(a.address_status, \\"\\") as address_status, ifnull(a.additional_information, \\"\\") as additional_information
            from sales_order_payment a
        ',
        'columns' => ['entity_id' => [PRIMARY],'parent_id' => [],'base_shipping_captured' => [NULLABLE],'shipping_captured' => [NULLABLE],'amount_refunded' => [NULLABLE],'base_amount_paid' => [NULLABLE],'amount_canceled' => [NULLABLE],'base_amount_authorized' => [NULLABLE],'base_amount_paid_online' => [NULLABLE],'base_amount_refunded_online' => [NULLABLE],'base_shipping_amount' => [NULLABLE],'shipping_amount' => [NULLABLE],'amount_paid' => [NULLABLE],'amount_authorized' => [NULLABLE],'base_amount_ordered' => [NULLABLE],'base_shipping_refunded' => [NULLABLE],'shipping_refunded' => [NULLABLE],'base_amount_refunded' => [NULLABLE],'amount_ordered' => [NULLABLE],'base_amount_canceled' => [NULLABLE],'quote_payment_id' => [NULLABLE],'additional_data' => [NULLABLE],'cc_exp_month' => [NULLABLE],'cc_ss_start_year' => [NULLABLE],'echeck_bank_name' => [NULLABLE],'method' => [NULLABLE],'cc_debug_request_body' => [NULLABLE],'cc_secure_verify' => [NULLABLE],'protection_eligibility' => [NULLABLE],'cc_approval' => [NULLABLE],'cc_last_4' => [NULLABLE],'cc_status_description' => [NULLABLE],'echeck_type' => [NULLABLE],'cc_debug_response_serialized' => [NULLABLE],'cc_ss_start_month' => [NULLABLE],'echeck_account_type' => [NULLABLE],'last_trans_id' => [NULLABLE],'cc_cid_status' => [NULLABLE],'cc_owner' => [NULLABLE],'cc_type' => [NULLABLE],'po_number' => [NULLABLE],'cc_exp_year' => [NULLABLE],'cc_status' => [NULLABLE],'echeck_routing_number' => [NULLABLE],'account_status' => [NULLABLE],'anet_trans_method' => [NULLABLE],'cc_debug_response_body' => [NULLABLE],'cc_ss_issue' => [NULLABLE],'echeck_account_name' => [NULLABLE],'cc_avs_status' => [NULLABLE],'cc_number_enc' => [NULLABLE],'cc_trans_id' => [NULLABLE],'address_status' => [NULLABLE],'additional_information' => [NULLABLE]],
    ],
    'sales_order_tax' => [
        'export_query' => '
            select 
                a.tax_id, a.order_id, ifnull(a.code, \\"\\") as code, ifnull(a.title, \\"\\") as title, ifnull(a.percent, \\"\\") as percent, ifnull(a.amount, \\"\\") as amount, a.priority, a.position, ifnull(a.base_amount, \\"\\") as base_amount, a.process, ifnull(a.base_real_amount, \\"\\") as base_real_amount
            from sales_order_tax a
        ',
        'columns' => ['tax_id' => [PRIMARY],'order_id' => [],'code' => [NULLABLE],'title' => [NULLABLE],'percent' => [NULLABLE],'amount' => [NULLABLE],'priority' => [],'position' => [],'base_amount' => [NULLABLE],'process' => [],'base_real_amount' => [NULLABLE]],
    ],
    'sales_order_tax_item' => [
        'export_query' => '
            select 
                a.tax_item_id, a.tax_id, ifnull(a.item_id, \\"\\") as item_id, a.tax_percent, a.amount, a.base_amount, a.real_amount, a.real_base_amount, ifnull(a.associated_item_id, \\"\\") as associated_item_id, a.taxable_item_type
            from sales_order_tax_item a
        ',
        'columns' => ['tax_item_id' => [PRIMARY],'tax_id' => [UNIQUE_KEY],'item_id' => [NULLABLE, UNIQUE_KEY],'tax_percent' => [],'amount' => [],'base_amount' => [],'real_amount' => [],'real_base_amount' => [],'associated_item_id' => [NULLABLE],'taxable_item_type' => []],
    ],
    // order invoices
    'sales_invoice' => [
        'export_query' => '
            select
                a.entity_id, ifnull(c.store_id, \\"\\") as store_id, ifnull(a.base_grand_total, \\"\\") as base_grand_total, ifnull(a.shipping_tax_amount, \\"\\") as shipping_tax_amount, ifnull(a.tax_amount, \\"\\") as tax_amount, ifnull(a.base_tax_amount, \\"\\") as base_tax_amount, ifnull(a.store_to_order_rate, \\"\\") as store_to_order_rate, ifnull(a.base_shipping_tax_amount, \\"\\") as base_shipping_tax_amount, ifnull(a.base_discount_amount, \\"\\") as base_discount_amount, ifnull(a.base_to_order_rate, \\"\\") as base_to_order_rate, ifnull(a.grand_total, \\"\\") as grand_total, ifnull(a.shipping_amount, \\"\\") as shipping_amount, ifnull(a.subtotal_incl_tax, \\"\\") as subtotal_incl_tax, ifnull(a.base_subtotal_incl_tax, \\"\\") as base_subtotal_incl_tax, ifnull(a.store_to_base_rate, \\"\\") as store_to_base_rate, ifnull(a.base_shipping_amount, \\"\\") as base_shipping_amount, ifnull(a.total_qty, \\"\\") as total_qty, ifnull(a.base_to_global_rate, \\"\\") as base_to_global_rate, ifnull(a.subtotal, \\"\\") as subtotal, ifnull(a.base_subtotal, \\"\\") as base_subtotal, ifnull(a.discount_amount, \\"\\") as discount_amount, ifnull(a.billing_address_id, \\"\\") as billing_address_id, ifnull(a.is_used_for_refund, \\"\\") as is_used_for_refund, a.order_id, ifnull(a.email_sent, \\"\\") as email_sent, ifnull(a.send_email, \\"\\") as send_email, ifnull(a.can_void_flag, \\"\\") as can_void_flag, ifnull(a.state, \\"\\") as state, ifnull(a.shipping_address_id, \\"\\") as shipping_address_id, ifnull(a.store_currency_code, \\"\\") as store_currency_code, ifnull(a.transaction_id, \\"\\") as transaction_id, ifnull(a.order_currency_code, \\"\\") as order_currency_code, ifnull(a.base_currency_code, \\"\\") as base_currency_code, ifnull(a.global_currency_code, \\"\\") as global_currency_code, ifnull(a.increment_id, \\"\\") as increment_id, a.created_at, a.updated_at, ifnull(a.discount_tax_compensation_amount, \\"\\") as discount_tax_compensation_amount, ifnull(a.base_discount_tax_compensation_amount, \\"\\") as base_discount_tax_compensation_amount, ifnull(a.shipping_discount_tax_compensation_amount, \\"\\") as shipping_discount_tax_compensation_amount, ifnull(a.base_shipping_discount_tax_compensation_amnt, \\"\\") as base_shipping_discount_tax_compensation_amnt, ifnull(a.shipping_incl_tax, \\"\\") as shipping_incl_tax, ifnull(a.base_shipping_incl_tax, \\"\\") as base_shipping_incl_tax, ifnull(a.base_total_refunded, \\"\\") as base_total_refunded, ifnull(a.discount_description, \\"\\") as discount_description, ifnull(a.customer_note, \\"\\") as customer_note, ifnull(a.customer_note_notify, \\"\\") as customer_note_notify
            from sales_invoice a
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code
        ',
        'columns' => ['entity_id' => [PRIMARY],'store_id' => [UNIQUE_KEY, NULLABLE],'base_grand_total' => [NULLABLE],'shipping_tax_amount' => [NULLABLE],'tax_amount' => [NULLABLE],'base_tax_amount' => [NULLABLE],'store_to_order_rate' => [NULLABLE],'base_shipping_tax_amount' => [NULLABLE],'base_discount_amount' => [NULLABLE],'base_to_order_rate' => [NULLABLE],'grand_total' => [NULLABLE],'shipping_amount' => [NULLABLE],'subtotal_incl_tax' => [NULLABLE],'base_subtotal_incl_tax' => [NULLABLE],'store_to_base_rate' => [NULLABLE],'base_shipping_amount' => [NULLABLE],'total_qty' => [NULLABLE],'base_to_global_rate' => [NULLABLE],'subtotal' => [NULLABLE],'base_subtotal' => [NULLABLE],'discount_amount' => [NULLABLE],'billing_address_id' => [NULLABLE],'is_used_for_refund' => [NULLABLE],'order_id' => [],'email_sent' => [NULLABLE],'send_email' => [NULLABLE],'can_void_flag' => [NULLABLE],'state' => [NULLABLE],'shipping_address_id' => [NULLABLE],'store_currency_code' => [NULLABLE],'transaction_id' => [NULLABLE],'order_currency_code' => [NULLABLE],'base_currency_code' => [NULLABLE],'global_currency_code' => [NULLABLE],'increment_id' => [UNIQUE_KEY, NULLABLE],'created_at' => [],'updated_at' => [],'discount_tax_compensation_amount' => [NULLABLE],'base_discount_tax_compensation_amount' => [NULLABLE],'shipping_discount_tax_compensation_amount' => [NULLABLE],'base_shipping_discount_tax_compensation_amnt' => [NULLABLE],'shipping_incl_tax' => [NULLABLE],'base_shipping_incl_tax' => [NULLABLE],'base_total_refunded' => [NULLABLE],'discount_description' => [NULLABLE],'customer_note' => [NULLABLE],'customer_note_notify' => [NULLABLE]],
    ],
    'sales_invoice_comment' => [
        'export_query' => '
            select
                a.entity_id, a.parent_id, ifnull(a.is_customer_notified, \\"\\") as is_customer_notified, a.is_visible_on_front, ifnull(a.comment, \\"\\") as comment, a.created_at
            from sales_invoice_comment a
        ',
        'columns' => ['entity_id' => [PRIMARY],'parent_id' => [],'is_customer_notified' => [NULLABLE],'is_visible_on_front' => [],'comment' => [NULLABLE],'created_at' => []],
    ],
    'sales_invoice_grid' => [
        'export_query' => '
            select
                a.entity_id, ifnull(a.increment_id, \\"\\") as increment_id, ifnull(a.state, \\"\\") as state, ifnull(c.store_id, \\"\\") as store_id, ifnull(a.store_name, \\"\\") as store_name, a.order_id, ifnull(a.order_increment_id, \\"\\") as order_increment_id, ifnull(a.order_created_at, \\"\\") as order_created_at, ifnull(a.customer_name, \\"\\") as customer_name, ifnull(a.customer_email, \\"\\") as customer_email, ifnull(a.customer_group_id, \\"\\") as customer_group_id, ifnull(a.payment_method, \\"\\") as payment_method, ifnull(a.store_currency_code, \\"\\") as store_currency_code, ifnull(a.order_currency_code, \\"\\") as order_currency_code, ifnull(a.base_currency_code, \\"\\") as base_currency_code, ifnull(a.global_currency_code, \\"\\") as global_currency_code, ifnull(a.billing_name, \\"\\") as billing_name, ifnull(a.billing_address, \\"\\") as billing_address, ifnull(a.shipping_address, \\"\\") as shipping_address, ifnull(a.shipping_information, \\"\\") as shipping_information, ifnull(a.subtotal, \\"\\") as subtotal, ifnull(a.shipping_and_handling, \\"\\") as shipping_and_handling, ifnull(a.grand_total, \\"\\") as grand_total, ifnull(a.base_grand_total, \\"\\") as base_grand_total, ifnull(a.created_at, \\"\\") as created_at, ifnull(a.updated_at, \\"\\") as updated_at
            from sales_invoice_grid a
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code
        ',
        'columns' => ['entity_id' => [PRIMARY],'increment_id' => [UNIQUE_KEY, NULLABLE],'state' => [NULLABLE],'store_id' => [UNIQUE_KEY, NULLABLE],'store_name' => [NULLABLE],'order_id' => [],'order_increment_id' => [NULLABLE],'order_created_at' => [NULLABLE],'customer_name' => [NULLABLE],'customer_email' => [NULLABLE],'customer_group_id' => [NULLABLE],'payment_method' => [NULLABLE],'store_currency_code' => [NULLABLE],'order_currency_code' => [NULLABLE],'base_currency_code' => [NULLABLE],'global_currency_code' => [NULLABLE],'billing_name' => [NULLABLE],'billing_address' => [NULLABLE],'shipping_address' => [NULLABLE],'shipping_information' => [NULLABLE],'subtotal' => [NULLABLE],'shipping_and_handling' => [NULLABLE],'grand_total' => [NULLABLE],'base_grand_total' => [NULLABLE],'created_at' => [NULLABLE],'updated_at' => [NULLABLE]],
    ],
    'sales_invoice_item' => [
        'export_query' => '
            select
                a.entity_id, a.parent_id, ifnull(a.base_price, \\"\\") as base_price, ifnull(a.tax_amount, \\"\\") as tax_amount, ifnull(a.base_row_total, \\"\\") as base_row_total, ifnull(a.discount_amount, \\"\\") as discount_amount, ifnull(a.row_total, \\"\\") as row_total, ifnull(a.base_discount_amount, \\"\\") as base_discount_amount, ifnull(a.price_incl_tax, \\"\\") as price_incl_tax, ifnull(a.base_tax_amount, \\"\\") as base_tax_amount, ifnull(a.base_price_incl_tax, \\"\\") as base_price_incl_tax, ifnull(a.qty, \\"\\") as qty, ifnull(a.base_cost, \\"\\") as base_cost, ifnull(a.price, \\"\\") as price, ifnull(a.base_row_total_incl_tax, \\"\\") as base_row_total_incl_tax, ifnull(a.row_total_incl_tax, \\"\\") as row_total_incl_tax, ifnull(a.product_id, \\"\\") as product_id, ifnull(a.order_item_id, \\"\\") as order_item_id, ifnull(a.additional_data, \\"\\") as additional_data, ifnull(a.description, \\"\\") as description, ifnull(a.sku, \\"\\") as sku, ifnull(a.name, \\"\\") as name, ifnull(a.discount_tax_compensation_amount, \\"\\") as discount_tax_compensation_amount, ifnull(a.base_discount_tax_compensation_amount, \\"\\") as base_discount_tax_compensation_amount, ifnull(a.tax_ratio, \\"\\") as tax_ratio, ifnull(a.weee_tax_applied, \\"\\") as weee_tax_applied, ifnull(a.weee_tax_applied_amount, \\"\\") as weee_tax_applied_amount, ifnull(a.weee_tax_applied_row_amount, \\"\\") as weee_tax_applied_row_amount, ifnull(a.weee_tax_disposition, \\"\\") as weee_tax_disposition, ifnull(a.weee_tax_row_disposition, \\"\\") as weee_tax_row_disposition, ifnull(a.base_weee_tax_applied_amount, \\"\\") as base_weee_tax_applied_amount, ifnull(a.base_weee_tax_applied_row_amnt, \\"\\") as base_weee_tax_applied_row_amnt, ifnull(a.base_weee_tax_disposition, \\"\\") as base_weee_tax_disposition, ifnull(a.base_weee_tax_row_disposition, \\"\\") as base_weee_tax_row_disposition
            from sales_invoice_item a
        ',
        'columns' => ['entity_id' => [PRIMARY],'parent_id' => [],'base_price' => [NULLABLE],'tax_amount' => [NULLABLE],'base_row_total' => [NULLABLE],'discount_amount' => [NULLABLE],'row_total' => [NULLABLE],'base_discount_amount' => [NULLABLE],'price_incl_tax' => [NULLABLE],'base_tax_amount' => [NULLABLE],'base_price_incl_tax' => [NULLABLE],'qty' => [NULLABLE],'base_cost' => [NULLABLE],'price' => [NULLABLE],'base_row_total_incl_tax' => [NULLABLE],'row_total_incl_tax' => [NULLABLE],'product_id' => [NULLABLE],'order_item_id' => [NULLABLE],'additional_data' => [NULLABLE],'description' => [NULLABLE],'sku' => [NULLABLE],'name' => [NULLABLE],'discount_tax_compensation_amount' => [NULLABLE],'base_discount_tax_compensation_amount' => [NULLABLE],'tax_ratio' => [NULLABLE],'weee_tax_applied' => [NULLABLE],'weee_tax_applied_amount' => [NULLABLE],'weee_tax_applied_row_amount' => [NULLABLE],'weee_tax_disposition' => [NULLABLE],'weee_tax_row_disposition' => [NULLABLE],'base_weee_tax_applied_amount' => [NULLABLE],'base_weee_tax_applied_row_amnt' => [NULLABLE],'base_weee_tax_disposition' => [NULLABLE],'base_weee_tax_row_disposition' => [NULLABLE]],
    ],
    // order credit memos
    'sales_creditmemo' => [
        'export_query' => '
            select
                a.entity_id, ifnull(c.store_id, \\"\\") as store_id, ifnull(a.adjustment_positive, \\"\\") as adjustment_positive, ifnull(a.base_shipping_tax_amount, \\"\\") as base_shipping_tax_amount, ifnull(a.store_to_order_rate, \\"\\") as store_to_order_rate, ifnull(a.base_discount_amount, \\"\\") as base_discount_amount, ifnull(a.base_to_order_rate, \\"\\") as base_to_order_rate, ifnull(a.grand_total, \\"\\") as grand_total, ifnull(a.base_adjustment_negative, \\"\\") as base_adjustment_negative, ifnull(a.base_subtotal_incl_tax, \\"\\") as base_subtotal_incl_tax, ifnull(a.shipping_amount, \\"\\") as shipping_amount, ifnull(a.subtotal_incl_tax, \\"\\") as subtotal_incl_tax, ifnull(a.adjustment_negative, \\"\\") as adjustment_negative, ifnull(a.base_shipping_amount, \\"\\") as base_shipping_amount, ifnull(a.store_to_base_rate, \\"\\") as store_to_base_rate, ifnull(a.base_to_global_rate, \\"\\") as base_to_global_rate, ifnull(a.base_adjustment, \\"\\") as base_adjustment, ifnull(a.base_subtotal, \\"\\") as base_subtotal, ifnull(a.discount_amount, \\"\\") as discount_amount, ifnull(a.subtotal, \\"\\") as subtotal, ifnull(a.adjustment, \\"\\") as adjustment, ifnull(a.base_grand_total, \\"\\") as base_grand_total, ifnull(a.base_adjustment_positive, \\"\\") as base_adjustment_positive, ifnull(a.base_tax_amount, \\"\\") as base_tax_amount, ifnull(a.shipping_tax_amount, \\"\\") as shipping_tax_amount, ifnull(a.tax_amount, \\"\\") as tax_amount, a.order_id, ifnull(a.email_sent, \\"\\") as email_sent, ifnull(a.send_email, \\"\\") as send_email, ifnull(a.creditmemo_status, \\"\\") as creditmemo_status, ifnull(a.state, \\"\\") as state, ifnull(a.shipping_address_id, \\"\\") as shipping_address_id, ifnull(a.billing_address_id, \\"\\") as billing_address_id, ifnull(a.invoice_id, \\"\\") as invoice_id, ifnull(a.store_currency_code, \\"\\") as store_currency_code, ifnull(a.order_currency_code, \\"\\") as order_currency_code, ifnull(a.base_currency_code, \\"\\") as base_currency_code, ifnull(a.global_currency_code, \\"\\") as global_currency_code, ifnull(a.transaction_id, \\"\\") as transaction_id, ifnull(a.increment_id, \\"\\") as increment_id, a.created_at, a.updated_at, ifnull(a.discount_tax_compensation_amount, \\"\\") as discount_tax_compensation_amount, ifnull(a.base_discount_tax_compensation_amount, \\"\\") as base_discount_tax_compensation_amount, ifnull(a.shipping_discount_tax_compensation_amount, \\"\\") as shipping_discount_tax_compensation_amount, ifnull(a.base_shipping_discount_tax_compensation_amnt, \\"\\") as base_shipping_discount_tax_compensation_amnt, ifnull(a.shipping_incl_tax, \\"\\") as shipping_incl_tax, ifnull(a.base_shipping_incl_tax, \\"\\") as base_shipping_incl_tax, ifnull(a.discount_description, \\"\\") as discount_description, ifnull(a.customer_note, \\"\\") as customer_note, ifnull(a.customer_note_notify, \\"\\") as customer_note_notify
            from sales_creditmemo a
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code
        ',
        'columns' => ['entity_id' => [PRIMARY],'store_id' => [UNIQUE_KEY, NULLABLE],'adjustment_positive' => [NULLABLE],'base_shipping_tax_amount' => [NULLABLE],'store_to_order_rate' => [NULLABLE],'base_discount_amount' => [NULLABLE],'base_to_order_rate' => [NULLABLE],'grand_total' => [NULLABLE],'base_adjustment_negative' => [NULLABLE],'base_subtotal_incl_tax' => [NULLABLE],'shipping_amount' => [NULLABLE],'subtotal_incl_tax' => [NULLABLE],'adjustment_negative' => [NULLABLE],'base_shipping_amount' => [NULLABLE],'store_to_base_rate' => [NULLABLE],'base_to_global_rate' => [NULLABLE],'base_adjustment' => [NULLABLE],'base_subtotal' => [NULLABLE],'discount_amount' => [NULLABLE],'subtotal' => [NULLABLE],'adjustment' => [NULLABLE],'base_grand_total' => [NULLABLE],'base_adjustment_positive' => [NULLABLE],'base_tax_amount' => [NULLABLE],'shipping_tax_amount' => [NULLABLE],'tax_amount' => [NULLABLE],'order_id' => [],'email_sent' => [NULLABLE],'send_email' => [NULLABLE],'creditmemo_status' => [NULLABLE],'state' => [NULLABLE],'shipping_address_id' => [NULLABLE],'billing_address_id' => [NULLABLE],'invoice_id' => [NULLABLE],'store_currency_code' => [NULLABLE],'order_currency_code' => [NULLABLE],'base_currency_code' => [NULLABLE],'global_currency_code' => [NULLABLE],'transaction_id' => [NULLABLE],'increment_id' => [UNIQUE_KEY, NULLABLE],'created_at' => [],'updated_at' => [],'discount_tax_compensation_amount' => [NULLABLE],'base_discount_tax_compensation_amount' => [NULLABLE],'shipping_discount_tax_compensation_amount' => [NULLABLE],'base_shipping_discount_tax_compensation_amnt' => [NULLABLE],'shipping_incl_tax' => [NULLABLE],'base_shipping_incl_tax' => [NULLABLE],'discount_description' => [NULLABLE],'customer_note' => [NULLABLE],'customer_note_notify' => [NULLABLE]],
    ],
    'sales_creditmemo_comment' => [
        'export_query' => '
            select
                a.entity_id, a.parent_id, ifnull(a.is_customer_notified, \\"\\") as is_customer_notified, a.is_visible_on_front, ifnull(a.comment, \\"\\") as comment, a.created_at
            from sales_creditmemo_comment a
        ',
        'columns' => ['entity_id' => [PRIMARY],'parent_id' => [],'is_customer_notified' => [NULLABLE],'is_visible_on_front' => [],'comment' => [NULLABLE],'created_at' => []],
    ],
    'sales_creditmemo_grid' => [
        'export_query' => '
            select
                a.entity_id, ifnull(a.increment_id, \\"\\") as increment_id, ifnull(a.created_at, \\"\\") as created_at, ifnull(a.updated_at, \\"\\") as updated_at, a.order_id, ifnull(a.order_increment_id, \\"\\") as order_increment_id, ifnull(a.order_created_at, \\"\\") as order_created_at, ifnull(a.billing_name, \\"\\") as billing_name, ifnull(a.state, \\"\\") as state, ifnull(a.base_grand_total, \\"\\") as base_grand_total, ifnull(a.order_status, \\"\\") as order_status, ifnull(c.store_id, \\"\\") as store_id, ifnull(a.billing_address, \\"\\") as billing_address, ifnull(a.shipping_address, \\"\\") as shipping_address, a.customer_name, ifnull(a.customer_email, \\"\\") as customer_email, ifnull(a.customer_group_id, \\"\\") as customer_group_id, ifnull(a.payment_method, \\"\\") as payment_method, ifnull(a.shipping_information, \\"\\") as shipping_information, ifnull(a.subtotal, \\"\\") as subtotal, ifnull(a.shipping_and_handling, \\"\\") as shipping_and_handling, ifnull(a.adjustment_positive, \\"\\") as adjustment_positive, ifnull(a.adjustment_negative, \\"\\") as adjustment_negative, ifnull(a.order_base_grand_total, \\"\\") as order_base_grand_total
            from sales_creditmemo_grid a
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code
        ',
        'columns' => ['entity_id' => [PRIMARY],'increment_id' => [UNIQUE_KEY, NULLABLE],'created_at' => [NULLABLE],'updated_at' => [NULLABLE],'order_id' => [],'order_increment_id' => [NULLABLE],'order_created_at' => [NULLABLE],'billing_name' => [NULLABLE],'state' => [NULLABLE],'base_grand_total' => [NULLABLE],'order_status' => [NULLABLE],'store_id' => [UNIQUE_KEY, NULLABLE],'billing_address' => [NULLABLE],'shipping_address' => [NULLABLE],'customer_name' => [],'customer_email' => [NULLABLE],'customer_group_id' => [NULLABLE],'payment_method' => [NULLABLE],'shipping_information' => [NULLABLE],'subtotal' => [NULLABLE],'shipping_and_handling' => [NULLABLE],'adjustment_positive' => [NULLABLE],'adjustment_negative' => [NULLABLE],'order_base_grand_total' => [NULLABLE]],
    ],
    'sales_creditmemo_item' => [
        'export_query' => '
            select
                a.entity_id, a.parent_id, ifnull(a.base_price, \\"\\") as base_price, ifnull(a.tax_amount, \\"\\") as tax_amount, ifnull(a.base_row_total, \\"\\") as base_row_total, ifnull(a.discount_amount, \\"\\") as discount_amount, ifnull(a.row_total, \\"\\") as row_total, ifnull(a.base_discount_amount, \\"\\") as base_discount_amount, ifnull(a.price_incl_tax, \\"\\") as price_incl_tax, ifnull(a.base_tax_amount, \\"\\") as base_tax_amount, ifnull(a.base_price_incl_tax, \\"\\") as base_price_incl_tax, ifnull(a.qty, \\"\\") as qty, ifnull(a.base_cost, \\"\\") as base_cost, ifnull(a.price, \\"\\") as price, ifnull(a.base_row_total_incl_tax, \\"\\") as base_row_total_incl_tax, ifnull(a.row_total_incl_tax, \\"\\") as row_total_incl_tax, ifnull(a.product_id, \\"\\") as product_id, ifnull(a.order_item_id, \\"\\") as order_item_id, ifnull(a.additional_data, \\"\\") as additional_data, ifnull(a.description, \\"\\") as description, ifnull(a.sku, \\"\\") as sku, ifnull(a.name, \\"\\") as name, ifnull(a.discount_tax_compensation_amount, \\"\\") as discount_tax_compensation_amount, ifnull(a.base_discount_tax_compensation_amount, \\"\\") as base_discount_tax_compensation_amount, ifnull(a.tax_ratio, \\"\\") as tax_ratio, ifnull(a.weee_tax_applied, \\"\\") as weee_tax_applied, ifnull(a.weee_tax_applied_amount, \\"\\") as weee_tax_applied_amount, ifnull(a.weee_tax_applied_row_amount, \\"\\") as weee_tax_applied_row_amount, ifnull(a.weee_tax_disposition, \\"\\") as weee_tax_disposition, ifnull(a.weee_tax_row_disposition, \\"\\") as weee_tax_row_disposition, ifnull(a.base_weee_tax_applied_amount, \\"\\") as base_weee_tax_applied_amount, ifnull(a.base_weee_tax_applied_row_amnt, \\"\\") as base_weee_tax_applied_row_amnt, ifnull(a.base_weee_tax_disposition, \\"\\") as base_weee_tax_disposition, ifnull(a.base_weee_tax_row_disposition, \\"\\") as base_weee_tax_row_disposition
            from sales_creditmemo_item a
        ',
        'columns' => ['entity_id' => [PRIMARY],'parent_id' => [],'base_price' => [NULLABLE],'tax_amount' => [NULLABLE],'base_row_total' => [NULLABLE],'discount_amount' => [NULLABLE],'row_total' => [NULLABLE],'base_discount_amount' => [NULLABLE],'price_incl_tax' => [NULLABLE],'base_tax_amount' => [NULLABLE],'base_price_incl_tax' => [NULLABLE],'qty' => [NULLABLE],'base_cost' => [NULLABLE],'price' => [NULLABLE],'base_row_total_incl_tax' => [NULLABLE],'row_total_incl_tax' => [NULLABLE],'product_id' => [NULLABLE],'order_item_id' => [NULLABLE],'additional_data' => [NULLABLE],'description' => [NULLABLE],'sku' => [NULLABLE],'name' => [NULLABLE],'discount_tax_compensation_amount' => [NULLABLE],'base_discount_tax_compensation_amount' => [NULLABLE],'tax_ratio' => [NULLABLE],'weee_tax_applied' => [NULLABLE],'weee_tax_applied_amount' => [NULLABLE],'weee_tax_applied_row_amount' => [NULLABLE],'weee_tax_disposition' => [NULLABLE],'weee_tax_row_disposition' => [NULLABLE],'base_weee_tax_applied_amount' => [NULLABLE],'base_weee_tax_applied_row_amnt' => [NULLABLE],'base_weee_tax_disposition' => [NULLABLE],'base_weee_tax_row_disposition' => [NULLABLE]],
    ],
    // order shipments
    'sales_shipment' => [
        'export_query' => '
            select 
                a.entity_id, ifnull(c.store_id, \\"\\") as store_id, ifnull(a.total_weight, \\"\\") as total_weight, ifnull(a.total_qty, \\"\\") as total_qty, ifnull(a.email_sent, \\"\\") as email_sent, ifnull(a.send_email, \\"\\") as send_email, a.order_id, ifnull(d.entity_id, \\"\\") as customer_id, ifnull(a.shipping_address_id, \\"\\") as shipping_address_id, ifnull(a.billing_address_id, \\"\\") as billing_address_id, ifnull(a.shipment_status, \\"\\") as shipment_status, ifnull(a.increment_id, \\"\\") as increment_id, a.created_at, a.updated_at, ifnull(a.packages, \\"\\") as packages, ifnull(a.shipping_label, \\"\\") as shipping_label, ifnull(a.customer_note, \\"\\") as customer_note, ifnull(a.customer_note_notify, \\"\\") as customer_note_notify, ifnull(a.udropship_vendor, \\"\\") as udropship_vendor, ifnull(a.udropship_status, \\"\\") as udropship_status, ifnull(a.base_total_value, \\"\\") as base_total_value, ifnull(a.total_value, \\"\\") as total_value, ifnull(a.base_shipping_amount, \\"\\") as base_shipping_amount, ifnull(a.shipping_amount, \\"\\") as shipping_amount, ifnull(a.base_shipping_tax, \\"\\") as base_shipping_tax, ifnull(a.shipping_tax, \\"\\") as shipping_tax, ifnull(a.base_shipping_amount_incl, \\"\\") as base_shipping_amount_incl, ifnull(a.shipping_amount_incl, \\"\\") as shipping_amount_incl, ifnull(a.base_discount_amount, \\"\\") as base_discount_amount, ifnull(a.base_tax_amount, \\"\\") as base_tax_amount, ifnull(a.base_hidden_tax_amount, \\"\\") as base_hidden_tax_amount, ifnull(a.total_cost, \\"\\") as total_cost, ifnull(a.transaction_fee, \\"\\") as transaction_fee, ifnull(a.commission_percent, \\"\\") as commission_percent, ifnull(a.handling_fee, \\"\\") as handling_fee, ifnull(a.udropship_available_at, \\"\\") as udropship_available_at, ifnull(a.udropship_method, \\"\\") as udropship_method, ifnull(a.udropship_method_description, \\"\\") as udropship_method_description, ifnull(a.udropship_shipcheck, \\"\\") as udropship_shipcheck, ifnull(a.udropship_vendor_order_id, \\"\\") as udropship_vendor_order_id, ifnull(a.udropship_payout_status, \\"\\") as udropship_payout_status, ifnull(a.statement_id, \\"\\") as statement_id, ifnull(a.statement_date, \\"\\") as statement_date
            from sales_shipment a
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code
                left join mapping_customer_entity d on a.customer_id = d.entity_id 
        ',
        'columns' => ['entity_id' => [PRIMARY],'store_id' => [UNIQUE_KEY, NULLABLE],'total_weight' => [NULLABLE],'total_qty' => [NULLABLE],'email_sent' => [NULLABLE],'send_email' => [NULLABLE],'order_id' => [],'customer_id' => [NULLABLE],'shipping_address_id' => [NULLABLE],'billing_address_id' => [NULLABLE],'shipment_status' => [NULLABLE],'increment_id' => [UNIQUE_KEY, NULLABLE],'created_at' => [],'updated_at' => [],'packages' => [NULLABLE],'shipping_label' => [NULLABLE],'customer_note' => [NULLABLE],'customer_note_notify' => [NULLABLE],'udropship_vendor' => [NULLABLE],'udropship_status' => [NULLABLE],'base_total_value' => [NULLABLE],'total_value' => [NULLABLE],'base_shipping_amount' => [NULLABLE],'shipping_amount' => [NULLABLE],'base_shipping_tax' => [NULLABLE],'shipping_tax' => [NULLABLE],'base_shipping_amount_incl' => [NULLABLE],'shipping_amount_incl' => [NULLABLE],'base_discount_amount' => [NULLABLE],'base_tax_amount' => [NULLABLE],'base_hidden_tax_amount' => [NULLABLE],'total_cost' => [NULLABLE],'transaction_fee' => [NULLABLE],'commission_percent' => [NULLABLE],'handling_fee' => [NULLABLE],'udropship_available_at' => [NULLABLE],'udropship_method' => [NULLABLE],'udropship_method_description' => [NULLABLE],'udropship_shipcheck' => [NULLABLE],'udropship_vendor_order_id' => [NULLABLE],'udropship_payout_status' => [NULLABLE],'statement_id' => [NULLABLE],'statement_date' => [NULLABLE]],
    ],
    'sales_shipment_comment' => [
        'export_query' => '
            select
                a.entity_id, a.parent_id, ifnull(a.is_customer_notified, \\"\\") as is_customer_notified, a.is_visible_on_front, ifnull(a.comment, \\"\\") as comment, a.created_at, ifnull(a.is_vendor_notified, \\"\\") as is_vendor_notified, ifnull(a.is_visible_to_vendor, \\"\\") as is_visible_to_vendor, ifnull(a.udropship_status, \\"\\") as udropship_status, ifnull(a.username, \\"\\") as username
            from sales_shipment_comment a
        ',
        'columns' => ['entity_id' => [PRIMARY],'parent_id' => [],'is_customer_notified' => [NULLABLE],'is_visible_on_front' => [],'comment' => [NULLABLE],'created_at' => [],'is_vendor_notified' => [NULLABLE],'is_visible_to_vendor' => [NULLABLE],'udropship_status' => [NULLABLE],'username' => [NULLABLE]],
    ],
    'sales_shipment_grid' => [
        'export_query' => '
            select
            a.entity_id, ifnull(a.increment_id, \\"\\") as increment_id, ifnull(c.store_id, \\"\\") as store_id, a.order_increment_id, a.order_id, a.order_created_at, a.customer_name, ifnull(a.total_qty, \\"\\") as total_qty, ifnull(a.shipment_status, \\"\\") as shipment_status, ifnull(a.order_status, \\"\\") as order_status, ifnull(a.billing_address, \\"\\") as billing_address, ifnull(a.shipping_address, \\"\\") as shipping_address, ifnull(a.billing_name, \\"\\") as billing_name, ifnull(a.shipping_name, \\"\\") as shipping_name, ifnull(a.customer_email, \\"\\") as customer_email, ifnull(a.customer_group_id, \\"\\") as customer_group_id, ifnull(a.payment_method, \\"\\") as payment_method, ifnull(a.shipping_information, \\"\\") as shipping_information, ifnull(a.created_at, \\"\\") as created_at, ifnull(a.updated_at, \\"\\") as updated_at, ifnull(a.udropship_vendor, \\"\\") as udropship_vendor, ifnull(a.udropship_status, \\"\\") as udropship_status, ifnull(a.base_shipping_amount, \\"\\") as base_shipping_amount, ifnull(a.shipping_amount, \\"\\") as shipping_amount, ifnull(a.base_discount_amount, \\"\\") as base_discount_amount, ifnull(a.udropship_method, \\"\\") as udropship_method, ifnull(a.udropship_method_description, \\"\\") as udropship_method_description, ifnull(a.udropship_payout_status, \\"\\") as udropship_payout_status, ifnull(a.statement_id, \\"\\") as statement_id, ifnull(a.statement_date, \\"\\") as statement_date
            from sales_shipment_grid a
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code
        ',
        'columns' => ['entity_id' => [PRIMARY],'increment_id' => [UNIQUE_KEY, NULLABLE],'store_id' => [UNIQUE_KEY, NULLABLE],'order_increment_id' => [],'order_id' => [],'order_created_at' => [],'customer_name' => [],'total_qty' => [NULLABLE],'shipment_status' => [NULLABLE],'order_status' => [NULLABLE],'billing_address' => [NULLABLE],'shipping_address' => [NULLABLE],'billing_name' => [NULLABLE],'shipping_name' => [NULLABLE],'customer_email' => [NULLABLE],'customer_group_id' => [NULLABLE],'payment_method' => [NULLABLE],'shipping_information' => [NULLABLE],'created_at' => [NULLABLE],'updated_at' => [NULLABLE],'udropship_vendor' => [NULLABLE],'udropship_status' => [NULLABLE],'base_shipping_amount' => [NULLABLE],'shipping_amount' => [NULLABLE],'base_discount_amount' => [NULLABLE],'udropship_method' => [NULLABLE],'udropship_method_description' => [NULLABLE],'udropship_payout_status' => [NULLABLE],'statement_id' => [NULLABLE],'statement_date' => [NULLABLE]],
    ],
    'sales_shipment_item' => [
        'export_query' => '
            select
                a.entity_id, a.parent_id, ifnull(a.row_total, \\"\\") as row_total, ifnull(a.price, \\"\\") as price, ifnull(a.weight, \\"\\") as weight, ifnull(a.qty, \\"\\") as qty, ifnull(a.product_id, \\"\\") as product_id, ifnull(a.order_item_id, \\"\\") as order_item_id, ifnull(a.additional_data, \\"\\") as additional_data, ifnull(a.description, \\"\\") as description, ifnull(a.name, \\"\\") as name, ifnull(a.sku, \\"\\") as sku, 0, ifnull(a.commission_percent, \\"\\") as commission_percent, ifnull(a.transaction_fee, \\"\\") as transaction_fee, ifnull(a.qty_shipped, \\"\\") as qty_shipped, ifnull(a.base_cost, \\"\\") as base_cost, ifnull(a.vendor_sku, \\"\\") as vendor_sku, ifnull(a.vendor_simple_sku, \\"\\") as vendor_simple_sku, ifnull(a.base_tax_amount, \\"\\") as base_tax_amount, ifnull(a.base_hidden_tax_amount, \\"\\") as base_hidden_tax_amount, ifnull(a.base_discount_amount, \\"\\") as base_discount_amount, ifnull(a.base_row_total, \\"\\") as base_row_total
            from sales_shipment_item a
        ',
        'columns' => ['entity_id' => [PRIMARY],'parent_id' => [],'row_total' => [NULLABLE],'price' => [NULLABLE],'weight' => [NULLABLE],'qty' => [NULLABLE],'product_id' => [NULLABLE],'order_item_id' => [NULLABLE],'additional_data' => [NULLABLE],'description' => [NULLABLE],'name' => [NULLABLE],'sku' => [NULLABLE],'qty_canceled' => [],'commission_percent' => [NULLABLE],'transaction_fee' => [NULLABLE],'qty_shipped' => [NULLABLE],'base_cost' => [NULLABLE],'vendor_sku' => [NULLABLE],'vendor_simple_sku' => [NULLABLE],'base_tax_amount' => [NULLABLE],'base_hidden_tax_amount' => [NULLABLE],'base_discount_amount' => [NULLABLE],'base_row_total' => [NULLABLE]],
    ],
    'sales_shipment_track' => [
        'export_query' => '
            select
                a.entity_id, a.parent_id, ifnull(a.weight, \\"\\") as weight, ifnull(a.qty, \\"\\") as qty, a.order_id, ifnull(a.track_number, \\"\\") as track_number, ifnull(a.description, \\"\\") as description, ifnull(a.title, \\"\\") as title, ifnull(a.carrier_code, \\"\\") as carrier_code, a.created_at, a.updated_at, ifnull(a.batch_id, \\"\\") as batch_id, ifnull(a.master_tracking_id, \\"\\") as master_tracking_id, ifnull(a.package_count, \\"\\") as package_count, ifnull(a.package_idx, \\"\\") as package_idx, ifnull(a.label_image, \\"\\") as label_image, ifnull(a.label_format, \\"\\") as label_format, ifnull(a.label_pic, \\"\\") as label_pic, ifnull(a.final_price, \\"\\") as final_price, ifnull(a.value, \\"\\") as value, ifnull(a.length, \\"\\") as length, ifnull(a.width, \\"\\") as width, ifnull(a.height, \\"\\") as height, ifnull(a.result_extra, \\"\\") as result_extra, ifnull(a.pkg_num, \\"\\") as pkg_num, ifnull(a.int_label_image, \\"\\") as int_label_image, ifnull(a.label_render_options, \\"\\") as label_render_options, ifnull(a.udropship_status, \\"\\") as udropship_status, ifnull(a.next_check, \\"\\") as next_check
            from sales_shipment_track a
        ',
        'columns' => ['entity_id' => [PRIMARY],'parent_id' => [],'weight' => [NULLABLE],'qty' => [NULLABLE],'order_id' => [],'track_number' => [NULLABLE],'description' => [NULLABLE],'title' => [NULLABLE],'carrier_code' => [NULLABLE],'created_at' => [],'updated_at' => [],'batch_id' => [NULLABLE],'master_tracking_id' => [NULLABLE],'package_count' => [NULLABLE],'package_idx' => [NULLABLE],'label_image' => [NULLABLE],'label_format' => [NULLABLE],'label_pic' => [NULLABLE],'final_price' => [NULLABLE],'value' => [NULLABLE],'length' => [NULLABLE],'width' => [NULLABLE],'height' => [NULLABLE],'result_extra' => [NULLABLE],'pkg_num' => [NULLABLE],'int_label_image' => [NULLABLE],'label_render_options' => [NULLABLE],'udropship_status' => [NULLABLE],'next_check' => [NULLABLE]],
    ],
    // cart coupons
    'salesrule' => [
        'export_query' => '
            select
                a.rule_id, ifnull(a.name, \\"\\") as name, ifnull(a.description, \\"\\") as description, ifnull(a.from_date, \\"\\") as from_date, ifnull(a.to_date, \\"\\") as to_date, a.uses_per_customer, a.is_active, ifnull(a.conditions_serialized, \\"\\") as conditions_serialized, ifnull(a.actions_serialized, \\"\\") as actions_serialized, a.stop_rules_processing, a.is_advanced, ifnull(a.product_ids, \\"\\") as product_ids, a.sort_order, ifnull(a.simple_action, \\"\\") as simple_action, a.discount_amount, ifnull(a.discount_qty, \\"\\") as discount_qty, a.discount_step, a.apply_to_shipping, a.times_used, a.is_rss, a.coupon_type, a.use_auto_generation, a.uses_per_coupon, a.simple_free_shipping
            from salesrule a
        ',
        'columns' => ['rule_id' => [PRIMARY],'name' => [NULLABLE],'description' => [NULLABLE],'from_date' => [NULLABLE],'to_date' => [NULLABLE],'uses_per_customer' => [],'is_active' => [],'conditions_serialized' => [NULLABLE],'actions_serialized' => [NULLABLE],'stop_rules_processing' => [],'is_advanced' => [],'product_ids' => [NULLABLE],'sort_order' => [],'simple_action' => [NULLABLE],'discount_amount' => [],'discount_qty' => [NULLABLE],'discount_step' => [],'apply_to_shipping' => [],'times_used' => [],'is_rss' => [],'coupon_type' => [],'use_auto_generation' => [],'uses_per_coupon' => [],'simple_free_shipping' => []],
    ],
    'salesrule_coupon' => [
        'export_query' => '
            select
                a.coupon_id, a.rule_id, ifnull(a.code, \\"\\") as code, ifnull(a.usage_limit, \\"\\") as usage_limit, ifnull(a.usage_per_customer, \\"\\") as usage_per_customer, a.times_used, ifnull(a.expiration_date, \\"\\") as expiration_date, ifnull(a.is_primary, \\"\\") as is_primary, ifnull(a.created_at, \\"\\") as created_at, ifnull(a.type, \\"\\") as type, ifnull(a.generated_by_dotmailer, \\"\\") as generated_by_dotmailer
            from salesrule_coupon a
        ',
        'columns' => ['coupon_id' => [PRIMARY],'rule_id' => [UNIQUE_KEY],'code' => [UNIQUE_KEY, NULLABLE],'usage_limit' => [NULLABLE],'usage_per_customer' => [NULLABLE],'times_used' => [],'expiration_date' => [NULLABLE],'is_primary' => [UNIQUE_KEY, NULLABLE],'created_at' => [NULLABLE],'type' => [NULLABLE],'generated_by_dotmailer' => [NULLABLE]],
    ],
    'salesrule_coupon_usage' => [
        'export_query' => '
            select
                a.coupon_id, a.customer_id, a.times_used
            from salesrule_coupon_usage a
                inner join mapping_customer_entity b on a.customer_id = b.entity_id
        ',
        'columns' => ['coupon_id' => [PRIMARY],'customer_id' => [PRIMARY],'times_used' => []],
    ],
    'salesrule_customer' => [
        'export_query' => '
            select
                a.rule_customer_id, a.rule_id, a.customer_id, a.times_used
            from salesrule_customer a
                inner join mapping_customer_entity b on a.customer_id = b.entity_id
        ',
        'columns' => ['rule_customer_id' => [PRIMARY],'rule_id' => [],'customer_id' => [],'times_used' => []],
    ],
    'salesrule_customer_group' => [
        'export_query' => '
            select
                a.rule_id, a.customer_group_id
            from salesrule_customer_group a
        ',
        'columns' => ['rule_id' => [PRIMARY],'customer_group_id' => [PRIMARY]],
    ],
    'salesrule_product_attribute' => [
        'export_query' => '
            select 
                a.rule_id, 
                case a.website_id
                    when 0 then 0
                    when 10 then 2
                    when 11 then 2
                    else 1 
                end as website_id,
                a.customer_group_id, 
                a.attribute_id
            from salesrule_product_attribute a
                inner join mapping_eav_attribute b on a.attribute_id = b.attribute_id 
        ',
        'columns' => ['rule_id' => [PRIMARY],'website_id' => [PRIMARY],'customer_group_id' => [PRIMARY],'attribute_id' => [PRIMARY]],
    ],
    'salesrule_website' => [
        'export_query' => '
            select
                a.rule_id, 
                case a.website_id
                    when 0 then 0
                    when 10 then 2
                    when 11 then 2
                    else 1 
                end as website_id
            from salesrule_website a
        ',
        'columns' => ['rule_id' => [PRIMARY],'website_id' => [PRIMARY]],
    ],
    // brands: extension not used anymore
    /*
    'brands_entity' => [
        'export_query' => '
            select entity_id, 0, 0 from mapping_mageplaza_brand group by option_id order by entity_id
        ',
        'columns' => ['entity_id' => [PRIMARY],'is_featured' => [],'enabled' => []],
    ],
    'brands_entity_int' => [
        'export_query' => '
            select 343 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, 0 as value from mapping_mageplaza_brand a where a.store_id = 0
            union all
            select 342 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, 0 as value from mapping_mageplaza_brand a where a.store_id = 0
            union all
            select 344 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, 1 as value from mapping_mageplaza_brand a where a.store_id = 0
            order by entity_id, attribute_id
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY],'store_id' => [UNIQUE_KEY],'entity_id' => [UNIQUE_KEY],'value' => []],
    ],
    'brands_entity_varchar' => [
        'export_query' => '
            select 335 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, ifnull(a.page_title, \\"\\") as value from mapping_mageplaza_brand a where a.store_id = 0
            union all
            select 341 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, ifnull(a.url_key, \\"\\") as value from mapping_mageplaza_brand a where a.store_id = 0
            union all
            select 337 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, ifnull(substring_index(a.image, \\"/\\", -1), \\"\\") as value from mapping_mageplaza_brand a where a.store_id = 0
            union all
            select 346 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, ifnull(a.meta_title, \\"\\") as value from mapping_mageplaza_brand a where a.store_id = 0
            union all
            select 348 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, \\"INDEX,FOLLOW\\" as value from mapping_mageplaza_brand a where a.store_id = 0
            order by entity_id 
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY],'store_id' => [UNIQUE_KEY],'entity_id' => [UNIQUE_KEY],'value' => [NULLABLE]],
    ],
    'brands_entity_text' => [
        'export_query' => '
            select 339 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, ifnull(a.short_description, \\"\\") as value from mapping_mageplaza_brand a where a.store_id = 0
            union all
            select 340 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, ifnull(a.description, \\"\\") as value from mapping_mageplaza_brand a where a.store_id = 0
            union all
            select 347 as attribute_id, a.store_id as store_id, a.entity_id as entity_id, ifnull(a.meta_description, \\"\\") as value from mapping_mageplaza_brand a where a.store_id = 0
            order by entity_id 
        ',
        'columns' => ['attribute_id' => [UNIQUE_KEY],'store_id' => [UNIQUE_KEY],'entity_id' => [UNIQUE_KEY],'value' => []],
    ],
    */
    // manufacturers
    /*
    'mageplaza_brand' => [
        'export_query' => '
            select 
                a.brand_id, a.option_id, c.store_id, ifnull(a.page_title, \\"\\") as page_title, a.url_key, ifnull(a.image, \\"\\") as image, a.is_featured, ifnull(a.short_description, \\"\\") as short_description, ifnull(CONCAT(ifnull(e.content, \\"\\"), a.description), \\"\\") as description, ifnull(a.static_block, \\"\\") as static_block, ifnull(a.meta_title, \\"\\") as meta_title, ifnull(a.meta_keywords, \\"\\") as meta_keywords, ifnull(a.meta_description, \\"\\") as meta_description
            from mageplaza_brand a 
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code
                inner join mapping_eav_attribute_option d on a.option_id = d.option_id
                left join cms_block e on a.static_block = e.identifier 
            order by a.brand_id
        ',
        'columns' => ['brand_id' => [PRIMARY],'option_id' => [],'store_id' => [],'page_title' => [NULLABLE],'url_key' => [],'image' => [NULLABLE],'is_featured' => [],'short_description' => [NULLABLE],'description' => [NULLABLE],'static_block' => [NULLABLE],'meta_title' => [NULLABLE],'meta_keywords' => [NULLABLE],'meta_description' => [NULLABLE]],
    ],
    'mageplaza_shopbybrand_brand_category' => [
        'export_query' => '
            select 
                a.cat_id, a.option_id, a.position
            from mageplaza_shopbybrand_brand_category a
                inner join mapping_eav_attribute_option b on a.option_id = b.option_id 
            order by a.cat_id, a.option_id, a.position
        ',
        'columns' => ['cat_id' => [PRIMARY, UNIQUE_KEY],'option_id' => [PRIMARY, UNIQUE_KEY],'position' => []],
    ],
    */
    // reviews and ratings
    'review' => [
        'export_query' => '
            select a.review_id, a.created_at, a.entity_id, a.entity_pk_value, a.status_id 
            from review a
        ',
        'columns' => ['review_id' => [PRIMARY], 'created_at' => [], 'entity_id' => [], 'entity_pk_value' => [], 'status_id' => []],
    ],
    'review_detail' => [
        'export_query' => '
            select 
                a.detail_id, a.review_id, ifnull(c.store_id, \\"\\") as store_id, a.title, a.detail, a.nickname, ifnull(a.customer_id, \\"\\") as customer_id
            from review_detail a
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code
            order by detail_id 
        ',
        'columns' => ['detail_id' => [PRIMARY],'review_id' => [],'store_id' => [NULLABLE],'title' => [],'detail' => [],'nickname' => [],'customer_id' => [NULLABLE]],
    ],
    'review_entity_summary' => [
        'export_query' => '
            select a.primary_id, a.entity_pk_value, a.entity_type, a.reviews_count, a.rating_summary, c.store_id 
            from review_entity_summary a
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code
            order by a.primary_id
        ',
        'columns' => ['primary_id' => [PRIMARY],'entity_pk_value' => [],'entity_type' => [],'reviews_count' => [],'rating_summary' => [],'store_id' => []],
    ],
    'review_store' => [
        'export_query' => '
            select
                a.review_id, c.store_id
            from review_store a
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code 
            order by c.store_id, a.review_id 
        ',
        'columns' => ['review_id' => [PRIMARY],'store_id' => [PRIMARY]],
    ],
    'rating_option_vote' => [
        'export_query' => '
            select 
            a.vote_id, a.option_id, a.remote_ip, a.remote_ip_long, ifnull(c.entity_id, \\"\\") as customer_id, a.entity_pk_value, a.rating_id, ifnull(a.review_id, \\"\\") as review_id, a.percent, a.value
            from rating_option_vote a 
                inner join mapping_catalog_product_entity b on a.entity_pk_value = b.entity_id 
                left join mapping_customer_entity c on a.customer_id = c.entity_id 
        ',
        'columns' => ['vote_id' => [PRIMARY],'option_id' => [],'remote_ip' => [],'remote_ip_long' => [],'customer_id' => [NULLABLE],'entity_pk_value' => [],'rating_id' => [],'review_id' => [NULLABLE],'percent' => [],'value' => []],
    ],
    'rating_option_vote_aggregated' => [
        'export_query' => '
            select
                a.primary_id, a.rating_id, a.entity_pk_value, a.vote_count, a.vote_value_sum, a.percent, ifnull(a.percent_approved, \\"\\") as percent_approved, d.store_id
            from rating_option_vote_aggregated a
                inner join mapping_catalog_product_entity b on a.entity_pk_value = b.entity_id 
                inner join store c on a.store_id = c.store_id 
                inner join mapping_store d on c.code = d.code 
        ',
        'columns' => ['primary_id' => [PRIMARY],'rating_id' => [],'entity_pk_value' => [],'vote_count' => [],'vote_value_sum' => [],'percent' => [],'percent_approved' => [NULLABLE],'store_id' => []],
    ],
    // xtento: skip until cronjobs can be imported
    /*
    'xtento_orderexport_destination' => [
        'export_query' => '
            select
                a.destination_id, a.name, a.type, a.hostname, ifnull(a.port, \\"\\") as port, a.username, a.password, a.timeout, a.path, a.ftp_type, a.ftp_pasv, a.email_sender, a.email_recipient, a.email_subject, a.email_body, a.email_html, a.email_attach_files, a.custom_class, a.custom_function, a.last_result, a.last_result_message, a.last_modification, 0 as email_send_files_separately, 0 as ftp_ignorepasvaddress, \\"\\" as email_bcc
            from xtento_orderexport_destination a
        ',
        'columns' => ['destination_id' => [PRIMARY],'name' => [],'type' => [],'hostname' => [],'port' => [NULLABLE],'username' => [],'password' => [],'timeout' => [],'path' => [],'ftp_type' => [],'ftp_pasv' => [],'email_sender' => [],'email_recipient' => [],'email_subject' => [],'email_body' => [],'email_html' => [],'email_attach_files' => [],'custom_class' => [],'custom_function' => [],'last_result' => [],'last_result_message' => [],'last_modification' => [],'email_send_files_separately' => [],'ftp_ignorepasvaddress' => [],'email_bcc' => []],
    ],
    'xtento_orderexport_log' => [
        'export_query' => '
            select
                a.log_id, a.created_at, a.profile_id, a.files, a.destination_ids, a.export_type, a.export_event, a.records_exported, a.result, a.result_message
            from xtento_orderexport_log a
        ',
        'columns' => ['log_id' => [PRIMARY],'created_at' => [],'profile_id' => [],'files' => [],'destination_ids' => [],'export_type' => [],'export_event' => [],'records_exported' => [],'result' => [],'result_message' => []],
    ],
    'xtento_orderexport_profile' => [
        'export_query' => '
            select
                a.profile_id, a.entity, a.enabled, a.name, a.destination_ids, a.last_execution, a.last_modification, a.conditions_serialized,
                case  
                    when a.store_ids IN (\\"28,29\\", \\"28,29,30\\") then \\"8,9\\"
                    when a.store_ids IN (\\"29\\") then \\"9\\"
                    when a.store_ids IN (\\"1,29\\") then \\"1,9\\"
                    when LENGTH(a.store_ids) > 50 then \\"\\"
                    when LENGTH(a.store_ids) > 10 then \\"1,2,3,4,5,6\\"
                    else a.store_ids 
                end as store_ids,
                a.export_fields, a.customer_groups, a.export_one_file_per_object, a.export_filter_new_only, ifnull(a.export_filter_datefrom, \\"\\") as export_filter_datefrom, ifnull(a.export_filter_dateto, \\"\\") as export_filter_dateto, ifnull(a.export_filter_older_x_minutes, \\"\\") as export_filter_older_x_minutes, ifnull(a.export_filter_last_x_days, \\"\\") as export_filter_last_x_days, a.export_filter_status, a.export_filter_product_type, a.export_action_change_status, ifnull(a.export_action_add_comment, \\"\\") as export_action_add_comment, a.export_action_cancel_order, a.export_action_invoice_order, a.export_action_invoice_notify, a.export_action_ship_order, a.export_action_ship_notify, a.save_files_manual_export, a.export_empty_files, a.manual_export_enabled, a.start_download_manual_export, a.save_files_local_copy, a.event_observers, a.cronjob_enabled, a.cronjob_frequency, a.cronjob_custom_frequency, a.output_type, a.filename, a.encoding, a.xsl_template, a.test_id
            from xtento_orderexport_profile a
        ',
        'columns' => ['profile_id' => [PRIMARY],'entity' => [],'enabled' => [],'name' => [],'destination_ids' => [],'last_execution' => [],'last_modification' => [],'conditions_serialized' => [],'store_ids' => [],'export_fields' => [],'customer_groups' => [],'export_one_file_per_object' => [],'export_filter_new_only' => [],'export_filter_datefrom' => [NULLABLE],'export_filter_dateto' => [NULLABLE],'export_filter_older_x_minutes' => [NULLABLE],'export_filter_last_x_days' => [NULLABLE],'export_filter_status' => [],'export_filter_product_type' => [],'export_action_change_status' => [],'export_action_add_comment' => [NULLABLE],'export_action_cancel_order' => [],'export_action_invoice_order' => [],'export_action_invoice_notify' => [],'export_action_ship_order' => [],'export_action_ship_notify' => [],'save_files_manual_export' => [],'export_empty_files' => [],'manual_export_enabled' => [],'start_download_manual_export' => [],'save_files_local_copy' => [],'event_observers' => [],'cronjob_enabled' => [],'cronjob_frequency' => [],'cronjob_custom_frequency' => [],'output_type' => [],'filename' => [],'encoding' => [],'xsl_template' => [],'test_id' => []],
    ],
    'xtento_orderexport_profile_history' => [
        'export_query' => '
            select a.history_id, a.profile_id, a.log_id, a.entity, a.entity_id, a.exported_at 
            from xtento_orderexport_profile_history a
        ',
        'columns' => ['history_id' => [PRIMARY],'profile_id' => [],'log_id' => [],'entity' => [],'entity_id' => [],'exported_at' => []],
    ],
    'xtento_orderimport_log' => [
        'export_query' => '
            select a.log_id, a.created_at, a.profile_id, a.files, a.source_ids, a.import_type, a.import_event, a.records_imported, a.result, a.result_message 
            from xtento_orderimport_log a
        ',
        'columns' => ['log_id' => [PRIMARY],'created_at' => [],'profile_id' => [],'files' => [],'source_ids' => [],'import_type' => [],'import_event' => [],'records_imported' => [],'result' => [],'result_message' => []],
    ],
    'xtento_orderimport_profile' => [
        'export_query' => '
            select a.profile_id, a.entity, a.processor, a.enabled, a.name, a.source_ids, a.last_execution, a.last_modification, a.conditions_serialized, a.cronjob_enabled, a.cronjob_frequency, a.cronjob_custom_frequency, a.configuration
            from xtento_orderimport_profile a
        ',
        'columns' => ['profile_id' => [PRIMARY],'entity' => [],'processor' => [],'enabled' => [],'name' => [],'source_ids' => [],'last_execution' => [],'last_modification' => [],'conditions_serialized' => [],'cronjob_enabled' => [],'cronjob_frequency' => [],'cronjob_custom_frequency' => [],'configuration' => []],
    ],
    'xtento_orderimport_profile_history' => [
        'export_query' => '
            select a.history_id, a.profile_id, a.log_id, a.entity, a.entity_id, a.increment_id, a.ext_order_id, a.log, a.import_data, a.imported_at, a.status
            from xtento_orderimport_profile_history a
        ',
        'columns' => ['history_id' => [PRIMARY],'profile_id' => [],'log_id' => [],'entity' => [],'entity_id' => [],'increment_id' => [],'ext_order_id' => [],'log' => [],'import_data' => [],'imported_at' => [],'status' => []],
    ],
    'xtento_orderimport_source' => [
        'export_query' => '
            select a.source_id, a.name, a.type, a.hostname, ifnull(a.port, \\"\\") as port, a.username, a.password, a.timeout, a.path, a.filename_pattern, a.archive_path, a.delete_imported_files, a.ftp_type, a.ftp_pasv, a.custom_class, a.custom_function, a.last_result, a.last_result_message, a.last_modification, a.ftp_ignorepasvaddress, a.skip_empty_files
            from xtento_orderimport_source a
        ',
        'columns' => ['source_id' => [PRIMARY],'name' => [],'type' => [],'hostname' => [],'port' => [NULLABLE],'username' => [],'password' => [],'timeout' => [],'path' => [],'filename_pattern' => [],'archive_path' => [],'delete_imported_files' => [],'ftp_type' => [],'ftp_pasv' => [],'custom_class' => [],'custom_function' => [],'last_result' => [],'last_result_message' => [],'last_modification' => [],'ftp_ignorepasvaddress' => [],'skip_empty_files' => []],
    ],
    'xtento_productexport_destination' => [
        'export_query' => '
            select a.destination_id, a.name, a.type, a.hostname, ifnull(a.port, \\"\\") as port, a.username, a.password, a.timeout, a.path, a.ftp_type, a.ftp_pasv, a.email_sender, a.email_recipient, a.email_subject, a.email_body, a.email_html, a.email_attach_files, a.custom_class, a.custom_function, a.last_result, a.last_result_message, a.last_modification, 0 as ftp_ignorepasvaddress, \\"\\" as email_bcc
            from xtento_productexport_destination a
        ',
        'columns' => ['destination_id' => [PRIMARY],'name' => [],'type' => [],'hostname' => [],'port' => [NULLABLE],'username' => [],'password' => [],'timeout' => [],'path' => [],'ftp_type' => [],'ftp_pasv' => [],'email_sender' => [],'email_recipient' => [],'email_subject' => [],'email_body' => [],'email_html' => [],'email_attach_files' => [],'custom_class' => [],'custom_function' => [],'last_result' => [],'last_result_message' => [],'last_modification' => [],'ftp_ignorepasvaddress' => [],'email_bcc' => []],
    ],
    'xtento_productexport_log' => [
        'export_query' => '
            select a.log_id, a.created_at, a.profile_id, a.files, a.destination_ids, a.export_type, a.export_event, a.records_exported, a.result, a.result_message
            from xtento_productexport_log a
        ',
        'columns' => ['log_id' => [PRIMARY],'created_at' => [],'profile_id' => [],'files' => [],'destination_ids' => [],'export_type' => [],'export_event' => [],'records_exported' => [],'result' => [],'result_message' => []],
    ],
    'xtento_productexport_profile' => [
        'export_query' => '
            select a.profile_id, a.entity, a.enabled, a.name, a.destination_ids, a.last_execution, a.last_modification, a.conditions_serialized, c.store_id, a.export_one_file_per_object, a.export_filter_new_only, ifnull(a.export_filter_datefrom, \\"\\") as export_filter_datefrom, ifnull(a.export_filter_dateto, \\"\\") as export_filter_dateto, ifnull(a.export_filter_older_x_minutes, \\"\\") as export_filter_older_x_minutes, ifnull(a.export_filter_last_x_days, \\"\\") as export_filter_last_x_days, ifnull(a.export_filter_updated_last_x_minutes, \\"\\") as export_filter_updated_last_x_minutes, a.export_filter_status, replace(a.export_filter_product_type, \\",giftvoucher\\", \\"\\"), a.export_filter_product_status, a.export_filter_product_visibility, a.export_filter_instock_only, a.save_files_manual_export, a.export_empty_files, a.customer_group_id, a.export_replace_nl_br, a.export_url_remove_store, a.export_strip_tags, a.manual_export_enabled, a.start_download_manual_export, a.save_files_local_copy, a.event_observers, a.cronjob_enabled, a.cronjob_frequency, a.cronjob_custom_frequency, a.output_type, a.attributes_to_select, a.filename, a.encoding, a.xsl_template, a.test_id, \\"\\" as category_mapping, \\"\\" as taxonomy_source, 1 as remove_pub_folder_from_urls
            from xtento_productexport_profile a
                left join store b on a.store_id = b.store_id 
                left join mapping_store c on b.code = c.code
            order by a.profile_id
        ',
        'columns' => ['profile_id' => [PRIMARY],'entity' => [],'enabled' => [],'name' => [],'destination_ids' => [],'last_execution' => [],'last_modification' => [],'conditions_serialized' => [],'store_id' => [],'export_one_file_per_object' => [],'export_filter_new_only' => [],'export_filter_datefrom' => [NULLABLE],'export_filter_dateto' => [NULLABLE],'export_filter_older_x_minutes' => [NULLABLE],'export_filter_last_x_days' => [NULLABLE],'export_filter_updated_last_x_minutes' => [NULLABLE],'export_filter_status' => [],'export_filter_product_type' => [],'export_filter_product_status' => [],'export_filter_product_visibility' => [],'export_filter_instock_only' => [],'save_files_manual_export' => [],'export_empty_files' => [],'customer_group_id' => [],'export_replace_nl_br' => [],'export_url_remove_store' => [],'export_strip_tags' => [],'manual_export_enabled' => [],'start_download_manual_export' => [],'save_files_local_copy' => [],'event_observers' => [],'cronjob_enabled' => [],'cronjob_frequency' => [],'cronjob_custom_frequency' => [],'output_type' => [],'attributes_to_select' => [],'filename' => [],'encoding' => [],'xsl_template' => [],'test_id' => [],'category_mapping' => [],'taxonomy_source' => [],'remove_pub_folder_from_urls' => []],
    ],
    'xtento_productexport_profile_history' => [
        'export_query' => '
            select a.history_id, a.profile_id, a.log_id, a.entity, a.entity_id, a.exported_at from xtento_productexport_profile_history a
        ',
        'columns' => ['history_id' => [PRIMARY],'profile_id' => [],'log_id' => [],'entity' => [],'entity_id' => [],'exported_at' => []],
    ],
    'xtento_stockimport_log' => [
        'export_query' => '
            select a.log_id, a.created_at, a.profile_id, a.files, a.source_ids, a.import_type, a.import_event, a.records_imported, a.result, a.result_message 
            from xtento_stockimport_log a
        ',
        'columns' => ['log_id' => [PRIMARY],'created_at' => [],'profile_id' => [],'files' => [],'source_ids' => [],'import_type' => [],'import_event' => [],'records_imported' => [],'result' => [],'result_message' => []],
    ],
    'xtento_stockimport_profile' => [
        'export_query' => '
            select a.profile_id, a.entity, a.processor, a.enabled, a.name, a.source_ids, a.last_execution, a.last_modification, a.conditions_serialized, a.cronjob_enabled, a.cronjob_frequency, a.cronjob_custom_frequency, a.configuration
            from xtento_stockimport_profile a
        ',
        'columns' => ['profile_id' => [PRIMARY],'entity' => [],'processor' => [],'enabled' => [],'name' => [],'source_ids' => [],'last_execution' => [],'last_modification' => [],'conditions_serialized' => [],'cronjob_enabled' => [],'cronjob_frequency' => [],'cronjob_custom_frequency' => [],'configuration' => []],
    ],
    'xtento_stockimport_source' => [
        'export_query' => '
            select a.source_id, a.name, a.type, a.hostname, ifnull(a.port, \\"\\") as port, a.username, a.password, a.timeout, a.path, a.filename_pattern, a.archive_path, a.delete_imported_files, a.ftp_type, a.ftp_pasv, a.custom_class, a.custom_function, a.last_result, a.last_result_message, a.last_modification, a.ftp_ignorepasvaddress, 0 as skip_empty_files 
            from xtento_stockimport_source a
        ',
        'columns' => ['source_id' => [PRIMARY],'name' => [],'type' => [],'hostname' => [],'port' => [NULLABLE],'username' => [],'password' => [],'timeout' => [],'path' => [],'filename_pattern' => [],'archive_path' => [],'delete_imported_files' => [],'ftp_type' => [],'ftp_pasv' => [],'custom_class' => [],'custom_function' => [],'last_result' => [],'last_result_message' => [],'last_modification' => [],'ftp_ignorepasvaddress' => [],'skip_empty_files' => []],
    ],
    'xtento_trackingimport_log' => [
        'export_query' => '
            select a.log_id, a.created_at, a.profile_id, a.files, a.source_ids, a.import_type, a.import_event, a.records_imported, a.result, a.result_message
            from xtento_trackingimport_log a
        ',
        'columns' => ['log_id' => [PRIMARY],'created_at' => [],'profile_id' => [],'files' => [],'source_ids' => [],'import_type' => [],'import_event' => [],'records_imported' => [],'result' => [],'result_message' => []],
    ],
    'xtento_trackingimport_profile' => [
        'export_query' => '
            select a.profile_id, a.entity, a.processor, a.enabled, a.name, a.source_ids, a.last_execution, a.last_modification, a.conditions_serialized, a.cronjob_enabled, a.cronjob_frequency, a.cronjob_custom_frequency, a.configuration
            from xtento_trackingimport_profile a
        ',
        'columns' => ['profile_id' => [PRIMARY],'entity' => [],'processor' => [],'enabled' => [],'name' => [],'source_ids' => [],'last_execution' => [],'last_modification' => [],'conditions_serialized' => [],'cronjob_enabled' => [],'cronjob_frequency' => [],'cronjob_custom_frequency' => [],'configuration' => []],
    ],
    'xtento_trackingimport_source' => [
        'export_query' => '
            select a.source_id, a.name, a.type, a.hostname, ifnull(a.port, \\"\\") as port, a.username, a.password, a.timeout, a.path, a.filename_pattern, a.archive_path, a.delete_imported_files, a.ftp_type, a.ftp_pasv, a.custom_class, a.custom_function, a.last_result, a.last_result_message, a.last_modification, 0 as ftp_ignorepasvaddress, 0 as skip_empty_files
            from xtento_trackingimport_source a
        ',
        'columns' => ['source_id' => [PRIMARY],'name' => [],'type' => [],'hostname' => [],'port' => [NULLABLE],'username' => [],'password' => [],'timeout' => [],'path' => [],'filename_pattern' => [],'archive_path' => [],'delete_imported_files' => [],'ftp_type' => [],'ftp_pasv' => [],'custom_class' => [],'custom_function' => [],'last_result' => [],'last_result_message' => [],'last_modification' => [],'ftp_ignorepasvaddress' => [],'skip_empty_files' => []],
    ],
    'xtento_xtcore_config_data' => [
        'export_query' => '
            select a.config_id, a.path, a.value from xtento_xtcore_config_data a
        ',
        'columns' => ['config_id' => [PRIMARY],'path' => [UNIQUE_KEY],'value' => []], // unique: path
    ],
    */
    // dropship
    'udropship_shipping' => [
        'export_query' => '
            select 
                a.shipping_id, a.shipping_code, a.shipping_title, a.days_in_transit, ifnull(a.vendor_ship_class, \\"\\") as vendor_ship_class, ifnull(a.customer_ship_class, \\"\\") as customer_ship_class
            from udropship_shipping a
            where a.shipping_id <> 4
        ',
        'columns' => ['shipping_id' => [PRIMARY],'shipping_code' => [],'shipping_title' => [],'days_in_transit' => [],'vendor_ship_class' => [NULLABLE],'customer_ship_class' => [NULLABLE]],
    ],
    'udropship_shipping_method' => [
        'export_query' => '
            select 
                a.shipping_id, a.carrier_code, a.method_code
            from udropship_shipping_method a
            where a.shipping_id <> 4
        ',
        'columns' => ['shipping_id' => [],'carrier_code' => [],'method_code' => []],
    ],
    'udropship_shipping_title' => [
        'export_query' => '
            select 
                a.title_id, a.shipping_id, c.store_id, a.title
            from udropship_shipping_title a
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code
            where a.shipping_id <> 4
        ',
        'columns' => ['title_id' => [PRIMARY],'shipping_id' => [UNIQUE_KEY],'store_id' => [UNIQUE_KEY],'title' => []],
    ],
    'udropship_shipping_website' => [
        'export_query' => '
            select 
                a.shipping_id, a.website_id
            from udropship_shipping_website a
            where a.shipping_id <> 4
        ',
        'columns' => ['shipping_id' => [PRIMARY],'website_id' => [PRIMARY]],
    ],
    'udropship_vendor' => [
        'export_query' => '
            select 
                a.vendor_id, a.vendor_name, a.vendor_attn, a.email, ifnull(a.telephone, \\"\\") as telephone, ifnull(a.fax, \\"\\") as fax, a.street, a.city, ifnull(a.zip, \\"\\") as zip, a.country_id, ifnull(a.region_id, \\"\\") as region_id, ifnull(a.region, \\"\\") as region, a.billing_use_shipping, a.billing_vendor_attn, a.billing_email, ifnull(a.billing_telephone, \\"\\") as billing_telephone, ifnull(a.billing_fax, \\"\\") as billing_fax, a.billing_street, a.billing_city, ifnull(a.billing_zip, \\"\\") as billing_zip, a.billing_country_id, ifnull(a.billing_region_id, \\"\\") as billing_region_id, ifnull(a.billing_region, \\"\\") as billing_region, a.status, ifnull(a.password, \\"\\") as password, ifnull(a.password_hash, \\"\\") as password_hash, ifnull(a.password_enc, \\"\\") as password_enc, ifnull(a.carrier_code, \\"\\") as carrier_code, a.notify_new_order, a.label_type, a.test_mode, a.handling_fee, a.ups_shipper_number, a.custom_data_combined, a.custom_vars_combined, a.email_template, ifnull(a.url_key, \\"\\") as url_key, ifnull(a.random_hash, \\"\\") as random_hash, ifnull(a.created_at, \\"\\") as created_at, a.use_handling_fee, a.allow_shipping_extra_charge, a.default_shipping_extra_charge_suffix, a.default_shipping_extra_charge_type, a.default_shipping_extra_charge, a.is_extra_charge_shipping_default, ifnull(a.default_shipping_id, \\"\\") as default_shipping_id, a.use_rates_fallback, a.notify_lowstock, a.notify_lowstock_qty, ifnull(a.tiercom_rates, \\"\\") as tiercom_rates, ifnull(a.tiercom_fixed_rule, \\"\\") as tiercom_fixed_rule, ifnull(a.tiercom_fixed_rates, \\"\\") as tiercom_fixed_rates, ifnull(a.tiercom_fixed_calc_type, \\"\\") as tiercom_fixed_calc_type, ifnull(a.tiership_use_v2_rates, \\"\\") as tiership_use_v2_rates, ifnull(a.vacation_mode, \\"\\") as vacation_mode, ifnull(a.vacation_end, \\"\\") as vacation_end, ifnull(a.vacation_message, \\"\\") as vacation_message, a.is_direct_shipping_possible
            from udropship_vendor a
        ',
        'columns' => ['vendor_id' => [PRIMARY],'vendor_name' => [],'vendor_attn' => [],'email' => [],'telephone' => [NULLABLE],'fax' => [NULLABLE],'street' => [],'city' => [],'zip' => [NULLABLE],'country_id' => [],'region_id' => [NULLABLE],'region' => [NULLABLE],'billing_use_shipping' => [],'billing_vendor_attn' => [],'billing_email' => [],'billing_telephone' => [NULLABLE],'billing_fax' => [NULLABLE],'billing_street' => [],'billing_city' => [],'billing_zip' => [NULLABLE],'billing_country_id' => [],'billing_region_id' => [NULLABLE],'billing_region' => [NULLABLE],'status' => [],'password' => [NULLABLE],'password_hash' => [NULLABLE],'password_enc' => [NULLABLE],'carrier_code' => [NULLABLE],'notify_new_order' => [],'label_type' => [],'test_mode' => [],'handling_fee' => [],'ups_shipper_number' => [],'custom_data_combined' => [],'custom_vars_combined' => [],'email_template' => [],'url_key' => [UNIQUE_KEY, NULLABLE],'random_hash' => [NULLABLE],'created_at' => [NULLABLE],'use_handling_fee' => [],'allow_shipping_extra_charge' => [],'default_shipping_extra_charge_suffix' => [],'default_shipping_extra_charge_type' => [],'default_shipping_extra_charge' => [],'is_extra_charge_shipping_default' => [],'default_shipping_id' => [NULLABLE],'use_rates_fallback' => [],'notify_lowstock' => [],'notify_lowstock_qty' => [],'tiercom_rates' => [NULLABLE],'tiercom_fixed_rule' => [NULLABLE],'tiercom_fixed_rates' => [NULLABLE],'tiercom_fixed_calc_type' => [NULLABLE],'tiership_use_v2_rates' => [NULLABLE],'vacation_mode' => [NULLABLE],'vacation_end' => [NULLABLE],'vacation_message' => [NULLABLE],'is_direct_shipping_possible' => []],
    ],
    'udropship_vendor_lowstock' => [
        'export_query' => '
            select 
                a.id, a.vendor_id, ifnull(a.product_id, \\"\\") as product_id, ifnull(a.notified_at, \\"\\") as notified_at, a.notified
            from udropship_vendor_lowstock a
        ',
        'columns' => ['id' => [PRIMARY],'vendor_id' => [UNIQUE_KEY],'product_id' => [UNIQUE_KEY, NULLABLE],'notified_at' => [NULLABLE],'notified' => []],
    ],
    'udropship_vendor_product_assoc' => [ 
        'export_query' => '
            select 
                a.vendor_id, a.product_id, a.is_attribute, a.is_udmulti, 0, 0, 1, 1
            from udropship_vendor_product_assoc a
                inner join mapping_catalog_product_entity b on a.product_id = b.entity_id 
        ',
        'columns' => ['vendor_id' => [PRIMARY],'product_id' => [PRIMARY],'is_attribute' => [],'is_udmulti' => [],'is_msi' => [],'as_parent' => [],'as_parent_udmulti' => [],'as_parent_msi' => []],
        'pre_import_query' => 'delete from udropship_vendor_product_assoc',
    ],
    'udropship_vendor_shipping' => [
        'export_query' => '
            select 
                a.vendor_shipping_id, a.vendor_id, a.shipping_id, ifnull(a.account_id, \\"\\") as account_id, ifnull(a.price_type, \\"\\") as price_type, ifnull(a.price, \\"\\") as price, a.priority, a.handling_fee, ifnull(a.carrier_code, \\"\\") as carrier_code, ifnull(a.est_carrier_code, \\"\\") as est_carrier_code, a.allow_extra_charge, ifnull(a.extra_charge_suffix, \\"\\") as extra_charge_suffix, ifnull(a.extra_charge_type, \\"\\") as extra_charge_type, a.extra_charge
            from udropship_vendor_shipping a
        ',
        'columns' => ['vendor_shipping_id' => [PRIMARY],'vendor_id' => [UNIQUE_KEY],'shipping_id' => [UNIQUE_KEY],'account_id' => [NULLABLE],'price_type' => [NULLABLE],'price' => [NULLABLE],'priority' => [],'handling_fee' => [],'carrier_code' => [NULLABLE],'est_carrier_code' => [NULLABLE],'allow_extra_charge' => [],'extra_charge_suffix' => [NULLABLE],'extra_charge_type' => [NULLABLE],'extra_charge' => []],
    ],
    // newsletter subscribers
    'newsletter_subscriber' => [
        'export_query' => '
            select
                a.subscriber_id, ifnull(c.store_id, \\"\\") as store_id, ifnull(a.change_status_at, \\"\\") as change_status_at, a.customer_id, ifnull(a.subscriber_email, \\"\\") as subscriber_email, a.subscriber_status, ifnull(a.subscriber_confirm_code, \\"\\") as subscriber_confirm_code
            from newsletter_subscriber a
                inner join store b on a.store_id = b.store_id 
                inner join mapping_store c on b.code = c.code 
        ',
        'columns' => ['subscriber_id' => [PRIMARY],'store_id' => [NULLABLE],'change_status_at' => [NULLABLE],'customer_id' => [],'subscriber_email' => [NULLABLE],'subscriber_status' => [],'subscriber_confirm_code' => [NULLABLE]],
    ],
    'email_template' => [
        'export_query' => '
            select
                a.template_id, a.template_code, a.template_text, ifnull(a.template_styles, \\"\\") as template_styles, ifnull(a.template_type, \\"\\") as template_type, a.template_subject, ifnull(a.template_sender_name, \\"\\") as template_sender_name, ifnull(a.template_sender_email, \\"\\") as template_sender_email, a.added_at, a.modified_at, ifnull(a.orig_template_code, \\"\\") as orig_template_code, ifnull(a.orig_template_variables, \\"\\") as orig_template_variables
            from email_template a
        ',
        'columns' => ['template_id' => [PRIMARY],'template_code' => [UNIQUE_KEY],'template_text' => [],'template_styles' => [NULLABLE],'template_type' => [NULLABLE],'template_subject' => [],'template_sender_name' => [NULLABLE],'template_sender_email' => [NULLABLE],'added_at' => [],'modified_at' => [],'orig_template_code' => [NULLABLE],'orig_template_variables' => [NULLABLE]],
    ],
    // order IDs
    'sequence_order_0' => [ // admin
        'export_query' => 'select * from sequence_order_0',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_order_3' => [ // bio_austria_at
        'export_query' => 'select * from sequence_order_20',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_order_1' => [ // myproduct_at
        'export_query' => 'select * from sequence_order_1',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_order_2' => [ // myproduct_de
        'export_query' => 'select * from sequence_order_12',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_order_8' => [ // erdbeerwoche_at
        'export_query' => 'select * from sequence_order_28',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_order_9' => [ // erdbeerwoche_de
        'export_query' => 'select * from sequence_order_29',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_order_4' => [ // mybier_at
        'export_query' => 'select * from sequence_order_8',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_order_5' => [ // products_of_life
        'export_query' => 'select * from sequence_order_21',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_order_6' => [ // so_schmeckt_noe_at
        'export_query' => 'select * from sequence_order_10',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    // creditmemo IDs
    'sequence_creditmemo_0' => [ // admin
        'export_query' => 'select * from sequence_creditmemo_0',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_creditmemo_3' => [ // bio_austria_at
        'export_query' => 'select * from sequence_creditmemo_20',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_creditmemo_1' => [ // myproduct_at
        'export_query' => 'select * from sequence_creditmemo_1',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_creditmemo_2' => [ // myproduct_de
        'export_query' => 'select * from sequence_creditmemo_12',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_creditmemo_8' => [ // erdbeerwoche_at
        'export_query' => 'select * from sequence_creditmemo_28',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_creditmemo_9' => [ // erdbeerwoche_de
        'export_query' => 'select * from sequence_creditmemo_29',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_creditmemo_4' => [ // mybier_at
        'export_query' => 'select * from sequence_creditmemo_8',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_creditmemo_5' => [ // products_of_life
        'export_query' => 'select * from sequence_creditmemo_21',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_creditmemo_6' => [ // so_schmeckt_noe_at
        'export_query' => 'select * from sequence_creditmemo_10',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    // invoice IDs
    'sequence_invoice_0' => [ // admin
        'export_query' => 'select * from sequence_invoice_0',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_invoice_3' => [ // bio_austria_at
        'export_query' => 'select * from sequence_invoice_20',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_invoice_1' => [ // myproduct_at
        'export_query' => 'select * from sequence_invoice_1',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_invoice_2' => [ // myproduct_de
        'export_query' => 'select * from sequence_invoice_12',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_invoice_8' => [ // erdbeerwoche_at
        'export_query' => 'select * from sequence_invoice_28',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_invoice_9' => [ // erdbeerwoche_de
        'export_query' => 'select * from sequence_invoice_29',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_invoice_4' => [ // mybier_at
        'export_query' => 'select * from sequence_invoice_8',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_invoice_5' => [ // products_of_life
        'export_query' => 'select * from sequence_invoice_21',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_invoice_6' => [ // so_schmeckt_noe_at
        'export_query' => 'select * from sequence_invoice_10',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    // shipment IDs
    'sequence_shipment_0' => [ // admin
        'export_query' => 'select * from sequence_shipment_0',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_shipment_3' => [ // bio_austria_at
        'export_query' => 'select * from sequence_shipment_20',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_shipment_1' => [ // myproduct_at
        'export_query' => 'select * from sequence_shipment_1',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_shipment_2' => [ // myproduct_de
        'export_query' => 'select * from sequence_shipment_12',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_shipment_8' => [ // erdbeerwoche_at
        'export_query' => 'select * from sequence_shipment_28',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_shipment_9' => [ // erdbeerwoche_de
        'export_query' => 'select * from sequence_shipment_29',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_shipment_4' => [ // mybier_at
        'export_query' => 'select * from sequence_shipment_8',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_shipment_5' => [ // products_of_life
        'export_query' => 'select * from sequence_shipment_21',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
    'sequence_shipment_6' => [ // so_schmeckt_noe_at
        'export_query' => 'select * from sequence_shipment_10',
        'columns' => ['sequence_value' => [PRIMARY]],
    ],
];

$updateTables = [
    'inventory_source_item' => [
        'update_query' => '
            insert into inventory_source_item (source_code, sku, quantity, status)
            select \'default\', sku, qty, stock_status 
                from cataloginventory_stock_status a 
                    inner join catalog_product_entity b on a.product_id = b.entity_id
        ',
        'columns' => ['source_code' => [UNIQUE_KEY], 'sku' => [UNIQUE_KEY], 'quantity' => [], 'status' => []],
    ],
];
?>
