{"name": "inspiredminds/myproduct", "description": "myProduct Magento 2 shop", "type": "project", "config": {"sort-packages": true, "platform": {"php": "8.2"}, "secure-http": false, "allow-plugins": {"cweagans/composer-patches": true, "dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/composer-dependency-version-audit-plugin": true, "magento/composer-root-update-plugin": true, "magento/inventory-composer-installer": true, "magento/magento-composer-installer": true, "php-http/discovery": true}}, "require": {"aheadworks/module-advanced-reports": "^2.11.3", "algolia/algoliasearch-inventory-magento-2": "^1.1", "algolia/algoliasearch-magento-2": "^3.14", "amasty/advanced-review": "^1.14", "amasty/module-mass-order-actions": "^1.5", "amasty/mostviewed": "^2.15", "amasty/special-occasion-coupons": "^1.0", "ampersand/magento2-disable-stock-reservation": "^1.0", "amzn/amazon-pay-magento-2-module": "^5.14", "catgento/module-admin-activity": "^1.1", "colinmollenhour/cache-backend-redis": "^1.17.1", "copex/module-vat-validation-frontend": "^1", "copex/module-vat-validator-austria": "^1", "creativestyle/magesuite": "^14", "creativestyle/magesuite-cart-bonus": "^1.0.0", "creativestyle/magesuite-content-constructor-admin": "^4.0.0", "creativestyle/magesuite-elasticsuite-virtual-category-indexer": "^1.1", "creativestyle/magesuite-free-gift": "^1.0.0", "creativestyle/magesuite-google-structured-data": "^2.0", "creativestyle/magesuite-lowest-price-logger": "^1.0.0", "creativestyle/magesuite-seo-link-masking": "^1.2", "cweagans/composer-patches": "^1.7", "ekomiltd/ekomiintegration": "^2.5.13", "element119/module-store-entity-creator": "^1.0", "elgentos/magento2-inventory-log": "^1.1", "fooman/pdfcustomiser-m2": "^8.6", "fuutur/module-campaignmonitor": "^2.0", "galacticlabs/customer-group-payment-filters": "^1.0.4", "klarna/m2-klarna": "^4", "laminas/laminas-mail": "^2.25", "laminas/laminas-mime": "^2.12", "m2e/kaufland-adobe-commerce": "^1.7", "m2epro/magento2-extension": "^1.0", "magenerds/baseprice": "^2.1", "magenerds/germanlaw": "^1.2", "magenerds/language-de_de": "^1.0", "magento/composer-root-update-plugin": "*", "magento/product-community-edition": "2.4.7-p7", "magepal/magento2-gmailsmtpapp": "^2.9", "mageplaza/magento-2-shop-by-brand": "^4.7.2", "mageworx/module-giftcards": "^3.0", "matomo/matomo-php-tracker": "^3.3", "mirakl/magento2-connect-seller-connector": "^1.1", "mollie/magento2": "^2.19", "olegkoval/magento2-regenerate-url-rewrites": "^1.6", "php-http/curl-client": "^2.3", "phpoffice/phpspreadsheet": "^1.28", "stripe/stripe-payments": "^4", "swissup/gdpr": "^1.5", "symfony/http-client": "^4.4 || ^5.1", "symfony/serializer": "^4.0", "webmozart/path-util": "^2.3", "xtento/module-stockimport": "^2.11", "xtento/orderexport": "^2.14", "xtento/orderimport": "^2.8.4", "xtento/productexport": "^2.14", "xtento/trackingimport": "^2.10", "yireo/magento2-taxratesmanager2": "^1.2", "zero1/stores-cli": "^1.0"}, "replace": {"dotmailer/dotmailer-magento2-extension": "*", "dotmailer/dotmailer-magento2-extension-b2b": "*", "dotmailer/dotmailer-magento2-extension-chat": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "dotmailer/dotmailer-magento2-extension-enterprise-package": "*", "dotmailer/dotmailer-magento2-extension-package": "*", "dotmailer/dotmailer-magento2-extension-sms": "*"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\Setup\\": "setup/src/Magento/Setup/", "Zend\\Mvc\\Controller\\": "setup/src/Zend/Mvc/Controller/"}}, "require-dev": {"deployer/deployer": "^7.1", "mage2tv/magento-cache-clean": "^1.0", "magento/magento-coding-standard": "^38.0", "symfony/var-dumper": "*"}, "conflict": {"gene/bluefoot": "*"}, "autoload-dev": {"psr-4": {"Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/", "Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/"}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"copex_packeton": {"type": "composer", "url": "https://myproduct:<EMAIL>"}, "copex": {"type": "composer", "url": "https://repo.copex.io/myproduct.at-9e961429112255b3cf7484b8a5af2d14"}, "magecomp": {"type": "composer", "url": "https://package.magecomp.com/"}, "ekomi": {"type": "vcs", "url": "https://github.com/ekomi-ltd/eKomi-integration-magento-2"}, "fooman": {"type": "composer", "url": "https://customer-repos.fooman.co.nz/myproduct.at-3997ca64087e37c4a9b3c99ef893a418e9193db1"}, "redchamps": {"type": "composer", "url": "https://RkIXo16HMUDYAKLXxu4WASILzmPS:<EMAIL>/repositories/<EMAIL>-zYvH9RJeSYPPDlyZLlC5VyCAZrfYE56Y"}, "aheadworks": {"type": "composer", "url": "https://dist.aheadworks.com/"}, "mageworx_packages": {"type": "composer", "url": "https://packages.mageworx.com/"}, "galacticlabs": {"type": "vcs", "url": "https://github.com/CopeX/customer-group-payment-filters"}, "0": {"type": "composer", "url": "https://repo.magento.com", "exclude": ["mollie/magento2"]}, "1": {"type": "composer", "url": "https://repo.xtento.com"}, "2": {"type": "composer", "url": "https://composer.bsscommerce.com/community"}, "3": {"type": "composer", "url": "https://composer.amasty.com/community/"}, "4": {"type": "composer", "url": "https://repo.mageplaza.com"}, "6": {"type": "composer", "url": "https://unirgy.repo.packagist.com/cxzkj-clxfn-7kpn1-9s/"}}, "extra": {"magento-force": "override", "patches": {"elgentos/magento2-inventory-log": {"add acl for grid": "patches/elgentos-inventory-log-enable-acl-grid.patch"}, "xtento/orderimport": {"Check stock qty during import": "patches/xtento-orderimport-stock-qty-check.patch"}, "xtento/orderexport": {"Add filter option for bundle products": "patches/xtento-orderexport-bundle-products-filter.patch"}, "klarna/module-core": {"Klarna logs error (https://docs.klarna.com/platform-solutions/magento/general/notice-for-users-of-magento-v243/)": "patches/klarna_logs.patch"}, "klarna/module-orderlines": {"fix for magento FPT": "patches/klarna-fpt.patch", "return when no FPT": "patches/fpt-tax-return-when-zero.patch"}, "aheadworks/module-raf": {"Klarna compatibility": "patches/aheadworks-module-raf-klarna.patch"}, "fooman/pdfcustomiser-implementation-m2": {"Fix tax table for Refer a Friend Extension": "patches/fooman-pdfcustomiser-implementation-m2-taxtable-refer-a-friend.patch", "Workaround for #681": "patches/fooman-pdfcustomiser-implementation-m2-tax-workaround.patch", "Make property protected": "patches/fooman-pdfcustomiser-implementation-m2-taxtable-protected-property.patch"}, "magento/framework": {"Fix incompabitility with fooman extensions": "patches/magento-framework-simpledirective.patch", "Fix error in calendar with php8": "patches/calendar-error-php8-38364.patch", "CVE-2025-54236": "patches/VULN-32437-2-4-X.diff"}, "magenerds/baseprice": {"Workaround for JS error": "patches/magenerds-baseprice-swatches.patch"}, "magento/module-sales": {"AC-1508": "patches/AC-1508-store-not-found-error.patch"}, "amasty/module-extra-fee": {"Collect rates patch": "patches/amasty-module-extra-fee-collect-rates.patch"}, "magecomp/magento-2-extra-fee": {"PHP 7 compatibility for AddExtraFee": "patches/Magecomp_Extrafee_AddExtraFee_php.patch"}, "smile/elasticsuite": {"remove string from return type": "patches/elastic-virtual-category-indexer.patch"}, "creativestyle/magesuite-email-attachments": {"allow dynamic properties": "patches/dynamic-property.patch", "email attachments add properties": "patches/email-attachments-add-properties.patch"}, "magento/module-weee": {"fix for FTP being added to subtotal https://github.com/magento/magento2/issues/39715": "patches/fix-deposit-subtotal.patch", "fix for FTP being added to subtotal in invoice https://github.com/magento/magento2/issues/39715": "patches/fix-deposit-subtotal-invoice.patch"}, "magento/module-offline-shipping": {"fix for bundle products free shipping": "patches/tablerate-bundle-children-free-shipping.patch"}, "paypal/module-braintree-core": {"disable braintree order grid": "patches/disable-braintree-order-grid.patch"}}, "magento-deploy-ignore": {"*": ["/.giti<PERSON>re"]}}}