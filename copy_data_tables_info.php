<?php

declare(strict_types=1);

if (\count($argv) < 2) {
    echo "Usage: php -f copy_data_tables_info.php <table> <options>\n\nPossible Options: print-columns\n";
    exit;
}

require_once('./copy_data_tables.php');
require_once('./copy_data_functions.php');

$table = $argv[1];

$remoteLoginPath = 'www.myproduct.at';
$remoteDatabase = 'usrdb_myproabr_live20201019';

$localConfig = '~/scripts/local_config.cnf';
$localDatabase = 'usrdb_myproabr_magento';

echo "--- Find differences ---\n";

$queryRemote = 'select COLUMN_NAME, IS_NULLABLE, COLUMN_KEY from information_schema.columns where table_schema = \"'.$remoteDatabase.'\" and table_name = \"'.$table.'\"';
$columnsArrayRemote = get_columns_array(remote_execute("mysql --login-path=$remoteLoginPath -e '$queryRemote' $remoteDatabase"));
$queryLocal = 'select COLUMN_NAME, IS_NULLABLE, COLUMN_KEY from information_schema.columns where table_schema = "'.$localDatabase.'" and table_name = "'.$table.'"';
$columnsArrayLocal = get_columns_array(\shell_exec("mysql --defaults-extra-file=$localConfig -e '$queryLocal' $localDatabase"));

$notExistingColumns = [];
foreach ($columnsArrayRemote as $column => $values) {
    if (!isset($columnsArrayLocal[$column])) {
        echo "Column $column does not exist!\n";
        $notExistingColumns[] = $column;
        continue;
    }
    if (!empty(array_diff($values, $columnsArrayLocal[$column]))) {
        echo "Column $column has different configurations!\n";
    }
}
foreach ($columnsArrayLocal as $column => $values) {
    if (!isset($columnsArrayRemote[$column])) {
        echo "Column $column is new!\n";
    }
}
echo "--- All differences listed ---\n";

if (isset($argv[2]) && $argv[2] === 'print-columns') {
    foreach ($notExistingColumns as $notExistingColumn) {
        unset($columnsArrayRemote[$notExistingColumn]);
    }
    echo "- Columns for query: (foreign columns not replaced) -\n";
    echo get_columns_string($columnsArrayRemote)."\n";
    echo "- Columns for array: (without unique keys) -\n";
    $search = ['array (', ')', "\n", "  ", "0 => ", "1 => ", ",]"];
    $replace = ['[', ']', "", "", "", "", "]"];
    echo str_replace($search, $replace, var_export($columnsArrayRemote, true))."\n";
}