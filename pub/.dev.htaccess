############################################
## Optional override of deployment mode. We recommend you use the
## command bin/magento deploy:mode:set to switch modes instead

# Options are default, production, or developer
#   SetEnv MAGE_MODE default

############################################
## Uncomment these lines for CGI mode.
## Make sure to specify the correct cgi php binary file name
## it might be /cgi-bin/php-cgi

#    Action php5-cgi /cgi-bin/php5-cgi
#    AddHandler php5-cgi .php

############################################
## GoDaddy specific options

#   Options -MultiViews

## You might also need to add this line to php.ini
##     cgi.fix_pathinfo = 1
## If it still doesn't work, rename php.ini to php5.ini

############################################
## Enable usage of methods arguments in backtrace

    #SetEnv MAGE_DEBUG_SHOW_ARGS 1

############################################
## This line is specific for 1and1 hosting

    #AddType x-mapp-php5 .php
    #AddHandler x-mapp-php5 .php

############################################
## Default index file

    DirectoryIndex index.php

<IfModule mod_php7.c>
############################################
## Adjust memory limit

    php_value memory_limit 756M
    php_value max_execution_time 18000

############################################
## Disable automatic session start
## before autoload was initialized

    php_flag session.auto_start off

############################################
## Enable resulting html compression

    #php_flag zlib.output_compression on

###########################################
# Disable user agent verification to not break multiple image upload

    php_flag suhosin.session.cryptua off
</IfModule>

<IfModule mod_security.c>
###########################################
# Disable POST processing to not break multiple image upload

    SecFilterEngine Off
    SecFilterScanPOST Off
</IfModule>

<IfModule mod_deflate.c>

############################################
## Enable apache served files compression
## http://developer.yahoo.com/performance/rules.html#gzip

    # Insert filter on all content
    ###SetOutputFilter DEFLATE
    # Insert filter on selected content types only
    #AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json image/svg+xml

    # Netscape 4.x has some problems...
    #BrowserMatch ^Mozilla/4 gzip-only-text/html

    # Netscape 4.06-4.08 have some more problems
    #BrowserMatch ^Mozilla/4\.0[678] no-gzip

    # MSIE masquerades as Netscape, but it is fine
    #BrowserMatch \bMSIE !no-gzip !gzip-only-text/html

    # Don't compress images
    #SetEnvIfNoCase Request_URI \.(?:gif|jpe?g|png)$ no-gzip dont-vary

    # Make sure proxies don't deliver the wrong content
    #Header append Vary User-Agent env=!dont-vary

</IfModule>

<IfModule mod_ssl.c>

############################################
## Make HTTPS env vars available for CGI mode

    SSLOptions StdEnvVars

</IfModule>

############################################
## Workaround for Apache 2.4.6 CentOS build when working via ProxyPassMatch with HHVM (or any other)
## Please, set it on virtual host configuration level

##    SetEnvIf Authorization "(.*)" HTTP_AUTHORIZATION=$1
############################################

<IfModule mod_rewrite.c>

############################################
## Enable rewrites

    Options +FollowSymLinks
    RewriteEngine on

############################################
## Maintenance mode

    RewriteCond %{DOCUMENT_ROOT}/maintenance.html -f
    RewriteCond %{DOCUMENT_ROOT}/maintenance.enable -f
    RewriteCond %{SCRIPT_FILENAME} !maintenance.html
    RewriteRule ^.*$ /maintenance.html [R=503,L]
    ErrorDocument 503 /maintenance.html

############################################
## You can put here your magento root folder
## path relative to web root

    #RewriteBase /magento/

############################################
## Workaround for HTTP authorization
## in CGI environment

    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

############################################
## TRACE and TRACK HTTP methods disabled to prevent XSS attacks

    RewriteCond %{REQUEST_METHOD} ^TRAC[EK]
    RewriteRule .* - [L,R=405]

############################################
## Redirect for mobile user agents

    #RewriteCond %{REQUEST_URI} !^/mobiledirectoryhere/.*$
    #RewriteCond %{HTTP_USER_AGENT} "android|blackberry|ipad|iphone|ipod|iemobile|opera mobile|palmos|webos|googlebot-mobile" [NC]
    #RewriteRule ^(.*)$ /mobiledirectoryhere/ [L,R=302]

############################################
## Never rewrite for existing files, directories and links

    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-l

############################################
## Rewrite everything else to index.php

    RewriteRule .* index.php [L]

</IfModule>


############################################
## Prevent character encoding issues from server overrides
## If you still have problems, use the second line instead

    AddDefaultCharset Off
    #AddDefaultCharset UTF-8
    AddType 'text/html; charset=UTF-8' html

<IfModule mod_expires.c>

############################################
## Add default Expires header
## http://developer.yahoo.com/performance/rules.html#expires

    ExpiresDefault "access plus 1 year"
    ExpiresByType text/html A0
    ExpiresByType text/plain A0

</IfModule>

###########################################
## Deny access to release notes to prevent disclosure of the installed Magento version

    <Files RELEASE_NOTES.txt>
        <IfVersion < 2.4>
            order allow,deny
            deny from all
        </IfVersion>
        <IfVersion >= 2.4>
            Require all denied
        </IfVersion>
    </Files>
    <Files .htaccess>
        <IfVersion < 2.4>
            order allow,deny
            deny from all
        </IfVersion>
        <IfVersion >= 2.4>
            Require all denied
        </IfVersion>
    </Files>
## Deny access  to cron.php
    <Files cron.php>
        <IfVersion < 2.4>
            order allow,deny
            deny from all
        </IfVersion>
        <IfVersion >= 2.4>
            Require all denied
        </IfVersion>
    </Files>
## Deny access  to .user.ini
    <Files .user.ini>
        <IfVersion < 2.4>
            order allow,deny
            deny from all
        </IfVersion>
        <IfVersion >= 2.4>
            Require all denied
        </IfVersion>
    </Files>

# For 404s and 403s that aren't handled by the application, show plain 404 response
ErrorDocument 404 /errors/404.php
ErrorDocument 403 /errors/404.php

################################
## If running in cluster environment, uncomment this
## http://developer.yahoo.com/performance/rules.html#etags

    #FileETag none

# ######################################################################
# # INTERNET EXPLORER                                                  #
# ######################################################################

# ----------------------------------------------------------------------
# | Document modes                                                     |
# ----------------------------------------------------------------------

# Force Internet Explorer 8/9/10 to render pages in the highest mode
# available in the various cases when it may not.
#
# https://hsivonen.fi/doctype/#ie8
#
# (!) Starting with Internet Explorer 11, document modes are deprecated.
# If your business still relies on older web apps and services that were
# designed for older versions of Internet Explorer, you might want to
# consider enabling `Enterprise Mode` throughout your company.
#
# https://msdn.microsoft.com/en-us/library/ie/bg182625.aspx#docmode
# http://blogs.msdn.com/b/ie/archive/2014/04/02/stay-up-to-date-with-enterprise-mode-for-internet-explorer-11.aspx

<IfModule mod_headers.c>
    ############################################
    Header set X-UA-Compatible "IE=edge"

    # `mod_headers` cannot match based on the content-type, however,
    # the `X-UA-Compatible` response header should be send only for
    # HTML documents and not for the other resources.
    <FilesMatch "\.(appcache|atom|bbaw|bmp|crx|css|cur|eot|f4[abpv]|flv|geojson|gif|htc|ico|jpe?g|js|json(ld)?|m4[av]|manifest|map|mp4|oex|og[agv]|opus|otf|pdf|png|rdf|rss|safariextz|svgz?|swf|topojson|tt[cf]|txt|vcard|vcf|vtt|webapp|web[mp]|webmanifest|woff2?|xloc|xml|xpi)$">
        Header unset X-UA-Compatible
    </FilesMatch>

    ## Prevent clickjacking
    Header set X-Frame-Options SAMEORIGIN
</IfModule>

AuthUserFile "/home/<USER>/dev.myproduct.at/.htpasswd"
AuthName "myProduct"
AuthType Basic
Require valid-user

SetEnvIf Host asm-dev.myproduct.at MAGE_RUN_CODE=austriansupermarket_at
SetEnvIf Host asm-dev.myproduct.at MAGE_RUN_TYPE=store

SetEnvIf Host droetker-dev.myproduct.at MAGE_RUN_CODE=dr_oetker_at
SetEnvIf Host droetker-dev.myproduct.at MAGE_RUN_TYPE=store

SetEnvIf Host manner-club-dev.myproduct.at MAGE_RUN_CODE=manner_club_shop_at
SetEnvIf Host manner-club-dev.myproduct.at MAGE_RUN_TYPE=store

SetEnvIf Host manner-dev.myproduct.at MAGE_RUN_CODE=manner_at
SetEnvIf Host manner-dev.myproduct.at MAGE_RUN_TYPE=store
