<?php

declare(strict_types=1);

if (\count($argv) < 3) {
    echo "Usage: php -f copy_data_from_old_magento.php <old_db_name> <new_db_name> <optional: tables (comma separated)>\n";
    exit;
}

require_once('./copy_data_tables.php');
require_once('./copy_data_functions.php');

$tablesToImport = isset($argv[3]) ? explode(',', $argv[3]) : array_keys($tables);

$remoteLoginPath = 'www.myproduct.at';
$remoteDatabase = $argv[1];

$localConfig = '~/scripts/local_config.cnf';
$localDatabase = $argv[2];

$remotePath = '~/mysqldumps/export/';
$remoteDump = $remotePath.'dump.zip';
$localPath = '~/scripts/import/';
$localDump = $localPath.'dump.zip';

echo "Start...\n";
remote_execute("mkdir -p $remotePath");
\shell_exec("mkdir -p $localPath");
\shell_exec("rm $localPath/*");

/**
 * Create mapping tables
 */
echo "--- create remote mapping tables ---\n";
foreach ($mappingTables as $name => $sql) {
    echo "- creating $name\n";
    if (strpos($name, 'mapping') === 0) { // so no production table are deleted by mistake
        $dropQuery = "DROP TABLE IF EXISTS $name;";
    }
    $createQuery = trim($sql['create_query']);
    remote_execute("mysql --login-path=$remoteLoginPath -e '$dropQuery' $remoteDatabase");
    remote_execute("mysql --login-path=$remoteLoginPath -e '$createQuery' $remoteDatabase");
}

/**
 * execute sql statement for each table
 */
echo "--- execute sql statement for each table ---\n";
foreach ($tables as $name => $sql) {
    if (!in_array($name, $tablesToImport, true)) {
        continue;
    }
    echo "- exporting $name\n";
    $query = trim($sql['export_query']);
    $remoteFile = $remotePath."dump_$name.csv";
    remote_execute("mysql --login-path=$remoteLoginPath -e '$query' $remoteDatabase > $remoteFile");
}

/**
 * compress results
 */
echo "--- compress results ---\n";
remote_execute("zip $remoteDump $remotePath*.csv");

/**
 * copy data to local server
 */
echo "--- copy data to local server ---\n";
\shell_exec("scp -P 12488 myproabr@*************:$remoteDump $localDump");

/**
 * decompress
 */
echo "--- decompress ---\n";
\shell_exec("unzip -j $localDump -d $localPath");

/**
 * import into local database
 */
echo "--- import into local database ---\n";
foreach ($tables as $table => $sql) {
    if (!in_array($table, $tablesToImport, true)) {
        continue;
    }

    if (isset($sql['pre_import_query'])) {
        $preImportQuery = $sql['pre_import_query'];
        \shell_exec("mysql --defaults-extra-file=$localConfig -e \"$preImportQuery\" $localDatabase");
    }
    
    $tmpTable = 'tmp_'.$table;
    
    // drop temp table if it exists
    \shell_exec("mysql --defaults-extra-file=$localConfig -e \"DROP TABLE IF EXISTS $tmpTable\" $localDatabase");

    // create temp table
    \shell_exec("mysql --defaults-extra-file=$localConfig -e \"CREATE TABLE $tmpTable SELECT * FROM $table WHERE 1=0\" $localDatabase");

    // import data into temp table
    echo "- importing $table\n";
    $importStatement = get_import_statement($sql['columns']);
    $localFile = $localPath."dump_$table.csv";
    \shell_exec("mysql --defaults-extra-file=$localConfig -e \"LOAD DATA LOCAL INFILE '$localFile' INTO TABLE $tmpTable CHARACTER SET UTF8 FIELDS TERMINATED BY '\\t' IGNORE 1 LINES $importStatement\" $localDatabase");

    // update original table
    $updateStatement = get_update_statement($sql['columns']);
    $columns = implode(', ', \array_keys($sql['columns']));
    \shell_exec("mysql --defaults-extra-file=$localConfig -e \"INSERT INTO $table ($columns) SELECT $columns FROM $tmpTable $updateStatement\" $localDatabase");
}

/**
 * perform local updates
 */
echo "--- perform local updates ---\n";
foreach ($updateTables as $name => $sql) {
    echo "- updating $name\n";
    $updateString = get_update_statement($sql['columns']);
    $query = trim($sql['update_query']).' '.$updateString;
    \shell_exec("mysql --defaults-extra-file=$localConfig -e \"$query\" $localDatabase");
    // echo "mysql --defaults-extra-file=$localConfig -e \"$query\" $localDatabase";
}

