--- a/vendor/klarna/module-core/Cron/CleanLogs.php
+++ b/vendor/klarna/module-core/Cron/CleanLogs.php
@@ -138,7 +138,6 @@ class CleanLogs
         $lifetime *= self::SECONDSINDAY;
 
         $logs = $this->logCollectionFactory->create();
-        $logs->addFieldToFilter('store_id', $store->getId());
         $logs->addFieldToFilter('created_at', ['to' => date("Y-m-d", time() - $lifetime)]);
 
         return $logs;
--- a/vendor/klarna/module-core/etc/db_schema.xml
+++ b/vendor/klarna/module-core/etc/db_schema.xml
@@ -36,7 +36,7 @@
     </table>
 
     <table name="klarna_logs" resource="default" engine="innodb" comment="Klarna Logs">
-        <column xsi:type="smallint" name="log_id" unsigned="false" nullable="false" identity="true" comment="Log Id"/>
+        <column xsi:type="int" name="log_id" unsigned="true" nullable="false" identity="true" comment="Log Id"/>
         <column xsi:type="varchar" name="status" nullable="false" length="255" comment="Status"/>
         <column xsi:type="varchar" name="action" length="255" comment="Action"/>
         <column xsi:type="varchar" name="klarna_id" length="255" comment="Klarna Id"/>
