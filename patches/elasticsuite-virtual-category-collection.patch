Index: vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php b/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php
--- a/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php
+++ b/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php	(date 1717440285000)
@@ -121,7 +121,7 @@
         $directionFallback = $sortBy !== 'position' ? Collection::SORT_ORDER_ASC : Collection::SORT_ORDER_DESC;

         $collection->setOrder($sortBy, $this->request->getParam('sort_direction', $directionFallback));
-        $collection->addPriceData(self::DEFAULT_CUSTOMER_GROUP_ID, $this->category->getStoreId());
+        $collection->addPriceData(self::DEFAULT_CUSTOMER_GROUP_ID, $this->category->getStore()->getWebsiteId());

         return $collection;
     }
