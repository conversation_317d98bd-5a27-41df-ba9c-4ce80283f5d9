--- a/vendor/xtento/module-stockimport/Model/Source/Sftp.php
+++ b/vendor/xtento/module-stockimport/Model/Source/Sftp.php
@@ -27,7 +27,9 @@
         $testResult = new DataObject();
         $this->setTestResult($testResult);
 
-        if (class_exists('\phpseclib\Net\SFTP')) { // Magento 2.1
+        if (class_exists('\phpseclib3\Net\SFTP')) {
+            $this->connection = new \phpseclib3\Net\SFTP($this->getDestination()->getHostname(), $this->getDestination()->getPort(), $this->getDestination()->getTimeout());
+        } elseif (class_exists('\phpseclib\Net\SFTP')) { // Magento 2.1
             $this->connection = new \phpseclib\Net\SFTP($this->getSource()->getHostname(), $this->getSource()->getPort(), $this->getSource()->getTimeout());
         } elseif (class_exists('\Net_SFTP')) { // Magento 2.0
             $this->connection = new \Net_SFTP($this->getSource()->getHostname(), $this->getSource()->getPort(), $this->getSource()->getTimeout());
