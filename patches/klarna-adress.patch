Index: vendor/klarna/module-base/Model/Quote/Address/Country.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/klarna/module-base/Model/Quote/Address/Country.php b/vendor/klarna/module-base/Model/Quote/Address/Country.php
--- a/vendor/klarna/module-base/Model/Quote/Address/Country.php	
+++ b/vendor/klarna/module-base/Model/Quote/Address/Country.php	(date 1737646012047)
@@ -65,10 +65,10 @@
     /**
      * Getting back the country from the address
      *
-     * @param Address $address
+     * @param $address
      * @return string|null
      */
-    private function getCountryByAddress(Address $address): ?string
+    private function getCountryByAddress($address): ?string
     {
         $country = $address->getCountry();
         if (empty($country)) {
