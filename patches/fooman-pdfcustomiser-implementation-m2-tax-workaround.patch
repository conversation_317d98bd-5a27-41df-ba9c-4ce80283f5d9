Index: vendor/fooman/pdfcustomiser-implementation-m2/src/Block/TaxTable.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/fooman/pdfcustomiser-implementation-m2/src/Block/TaxTable.php b/vendor/fooman/pdfcustomiser-implementation-m2/src/Block/TaxTable.php
--- a/vendor/fooman/pdfcustomiser-implementation-m2/src/Block/TaxTable.php
+++ b/vendor/fooman/pdfcustomiser-implementation-m2/src/Block/TaxTable.php	(date 1736795987740)
@@ -114,10 +114,10 @@
             foreach ($taxes as $tax) {
                 $this->checkIfunallocatedApplies($tax);
                 $tax['base_tax_basis'] = $this->formatCurrency(
-                    $this->accumulatedTaxes[$tax['percent']]['base_tax_basis']
+                    $this->accumulatedTaxes[$tax['percent']]['base_tax_basis']??0
                 );
                 $tax['base_subtotal'] = $this->formatCurrency(
-                    $this->accumulatedTaxes[$tax['percent']]['base_tax_basis']
+                    $this->accumulatedTaxes[$tax['percent']]['base_tax_basis']??0
                     +  $this->accumulatedTaxes[$tax['percent']]['base_tax_amount']
                 );
                 $tax['base_tax_amount'] = $this->formatCurrency(
