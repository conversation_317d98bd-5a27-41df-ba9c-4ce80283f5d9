--- a/vendor/m2epro/magento2-extension/Model/Amazon/Order.php
--- b/vendor/m2epro/magento2-extension/Model/Amazon/Order.php
@@ -756,7 +756,7 @@
      */
     public function updateShippingStatus(array $trackingDetails = [], array $items = [])
     {
-        if (!$this->canUpdateShippingStatus($trackingDetails)) {
+        if (!$this->canUpdateShippingStatus($trackingDetails) || empty($trackingDetails['carrier_code'])) {
             return false;
         }
 
@@ -764,12 +764,10 @@
             $trackingDetails['fulfillment_date'] = $this->getHelper('Data')->getCurrentGmtDate();
         }
 
-        if (!empty($trackingDetails['carrier_code'])) {
-            $trackingDetails['carrier_title'] = $this->getHelper('Component_Amazon')->getCarrierTitle(
-                $trackingDetails['carrier_code'],
-                isset($trackingDetails['carrier_title']) ? $trackingDetails['carrier_title'] : ''
+        $trackingDetails['carrier_title'] = $this->getHelper('Component_Amazon')->getCarrierTitle(
+            $trackingDetails['carrier_code'],
+            isset($trackingDetails['carrier_title']) ? $trackingDetails['carrier_title'] : ''
             );
-        }
 
         if (!empty($trackingDetails['carrier_title'])) {
             if ($trackingDetails['carrier_title'] == \Ess\M2ePro\Model\Order\Shipment\Handler::CUSTOM_CARRIER_CODE &&
