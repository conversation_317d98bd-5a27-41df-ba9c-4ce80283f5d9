diff --git a/vendor/magento/framework/Filter/DirectiveProcessor/VarDirective.php b/vendor/magento/framework/Filter/DirectiveProcessor/VarDirective.php
index f2fe398c3848..78034d70ba51 100644
--- a/vendor/magento/framework/Filter/DirectiveProcessor/VarDirective.php
+++ b/vendor/magento/framework/Filter/DirectiveProcessor/VarDirective.php
@@ -55,6 +55,11 @@ public function process(array $construction, Template $filter, array $templateVa
             $result = $this->filterApplier->applyFromRawParam($construction['filters'], $result);
         }
 
+        $pattern = '/{{.*?}}/';
+        do {
+            $result = preg_replace($pattern, '', (string)$result);
+        } while (preg_match($pattern, $result));
+
         return $result;
     }
 
