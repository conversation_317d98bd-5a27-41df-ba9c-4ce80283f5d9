--- a/vendor/amasty/module-extra-fee/Model/FeeRepository.php
+++ b/vendor/amasty/module-extra-fee/Model/FeeRepository.php
@@ -205,7 +205,7 @@ class FeeRepository implements FeeRepositoryInterface
             $address = $quote->getBillingAddress();
         }
 
-        $address->setCollectShippingRates(true);
+        //$address->setCollectShippingRates(true);
         $address->collectShippingRates();
         $address->setData('total_qty', $quote->getData('items_qty'));
         $address->setData('base_subtotal', $quote->getData('base_subtotal'));
