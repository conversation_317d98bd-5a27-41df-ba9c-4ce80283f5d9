Index: app/code/InspiredMinds/CustomDropship/Model/Pdf/Items/ShipmentRenderer.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/code/InspiredMinds/CustomDropship/Model/Pdf/Items/ShipmentRenderer.php b/app/code/InspiredMinds/CustomDropship/Model/Pdf/Items/ShipmentRenderer.php
--- a/app/code/InspiredMinds/CustomDropship/Model/Pdf/Items/ShipmentRenderer.php
+++ b/app/code/InspiredMinds/CustomDropship/Model/Pdf/Items/ShipmentRenderer.php	(date 1711007482299)
@@ -71,13 +71,13 @@
         $lines = [];

         // draw Product name
-        $lines[0] = [['text' => $this->string->split($item->getName(), 60, true, true), 'feed' => 100, 'height' => $heightOpt]];
+        $lines[0] = [['text' => $this->string->split($item->getName()??"", 60, true, true), 'feed' => 100, 'height' => $heightOpt]];

         // draw QTY
-        $lines[0][] = ['text' => $item->getQty() * 1, 'feed' => 35, 'height' => $heightOpt];
+        $lines[0][] = ['text' => $item->getQty() * 1??"", 'feed' => 35, 'height' => $heightOpt];

         // draw supplier SKU
-        $lines[0][] = ['text' => $product->getSupplierSku(), 'feed' => 380, 'height' => $heightOpt];
+        $lines[0][] = ['text' => $product->getSupplierSku()??"", 'feed' => 380, 'height' => $heightOpt];

         // draw SKU
         $lines[0][] = [
