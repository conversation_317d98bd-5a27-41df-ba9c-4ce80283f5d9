Index: vendor/magento/module-weee/Model/Total/Quote/WeeeTax.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/magento/module-weee/Model/Total/Quote/WeeeTax.php b/vendor/magento/module-weee/Model/Total/Quote/WeeeTax.php
--- a/vendor/magento/module-weee/Model/Total/Quote/WeeeTax.php
+++ b/vendor/magento/module-weee/Model/Total/Quote/WeeeTax.php	(date 1741336482517)
@@ -224,13 +224,14 @@
         if ($this->weeeData->includeInSubtotal($this->_store)) {
             $total->addTotalAmount('subtotal', $rowValueExclTax);
             $total->addBaseTotalAmount('subtotal', $baseRowValueExclTax);
+            $total->setSubtotalInclTax($total->getSubtotalInclTax() + $rowValueInclTax);
+            $total->setBaseSubtotalInclTax($total->getBaseSubtotalInclTax() + $baseRowValueInclTax);
         } else {
             $total->addTotalAmount('weee', $rowValueExclTax);
             $total->addBaseTotalAmount('weee', $baseRowValueExclTax);
         }

-        $total->setSubtotalInclTax($total->getSubtotalInclTax() + $rowValueInclTax);
-        $total->setBaseSubtotalInclTax($total->getBaseSubtotalInclTax() + $baseRowValueInclTax);
+
         $address->setBaseSubtotalTotalInclTax($total->getBaseSubtotalInclTax());
         $address->setSubtotalInclTax($total->getSubtotalInclTax());
         return $this;


