Index: vendor/xtento/orderimport/Model/Import/Processor/Order/Item.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/xtento/orderimport/Model/Import/Processor/Order/Item.php b/vendor/xtento/orderimport/Model/Import/Processor/Order/Item.php
--- a/vendor/xtento/orderimport/Model/Import/Processor/Order/Item.php
+++ b/vendor/xtento/orderimport/Model/Import/Processor/Order/Item.php	(date 1710756290132)
@@ -57,6 +57,18 @@
      */
     protected $scopeConfig;

+    /**
+     * @var \Magento\InventorySalesApi\Api\IsProductSalableForRequestedQtyInterface
+     */
+    protected $isProductSalableForRequestedQtyInterface;
+
+    /**
+     * @var \Magento\InventoryCatalogApi\Api\DefaultStockProviderInterface
+     */
+    protected $defaultStockProviderInterface;
+
+    /**
+
     /**
      * @param Registry $registry
      * @param \Magento\Tax\Model\Calculation $taxCalculation
@@ -66,6 +78,8 @@
      * @param \Magento\Framework\Event\ManagerInterface $eventManager
      * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
      * @param Utils $utilsHelper
+     * @param \Magento\CatalogInventory\Api\StockStateInterface $stockState
+     *
      * @param array $data
      */
     public function __construct(
@@ -77,6 +91,8 @@
         \Magento\Framework\Event\ManagerInterface $eventManager,
         \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
         Utils $utilsHelper,
+        \Magento\InventorySalesApi\Api\IsProductSalableForRequestedQtyInterface $isProductSalableForRequestedQtyInterface,
+        \Magento\InventoryCatalogApi\Api\DefaultStockProviderInterface $defaultStockProviderInterface,
         array $data = []
     ) {
         parent::__construct($data);
@@ -88,6 +104,8 @@
         $this->eventManager = $eventManager;
         $this->utilsHelper = $utilsHelper;
         $this->scopeConfig = $scopeConfig;
+        $this->isProductSalableForRequestedQtyInterface = $isProductSalableForRequestedQtyInterface;
+        $this->defaultStockProviderInterface = $defaultStockProviderInterface;
     }

     /**
@@ -204,6 +222,14 @@
                     }
                 }

+                // Qty requested
+                $qty = isset($itemData['qty_ordered']) ? $itemData['qty_ordered'] : 1;
+                if (!is_numeric($qty)) {
+                    $warnings[] = __('Qty of product contains invalid value, falling back to qty 1.');
+                    $qty = 1;
+                }
+
+
                 if (!$productId) {
                     if (!$isMagentoMsiEnabled) {
                         $productId = 99999999;
@@ -218,22 +244,17 @@
                     $product = $this->productFactory->create()->setStoreId($order->getStoreId())->load($productId);

                     $skipOutOfStockProducts = $this->getConfigFlag('skip_out_of_stock_products');
-                    if ($skipOutOfStockProducts && !$product->isSaleable()) {
+                    $defaultStock = $this->defaultStockProviderInterface->getId();
+                    $isStockEnough = $this->isProductSalableForRequestedQtyInterface->execute($product->getSku(), $defaultStock, $qty);
+                    if ($skipOutOfStockProducts && (!$product->isSaleable() || !$isStockEnough->isSalable())) {
                         $warnings[] = __('Product SKU "%1" is out of stock, skipping', $product->getSku());
                         continue;
                     }
                 }
                 $item->setStoreId($order->getStoreId());
                 $item->setProductId($productId);
-
-                // Customer group / Qty requested
+                // Customer group
                 $customerGroupId = Group::NOT_LOGGED_IN_ID;
-                $qty = isset($itemData['qty_ordered']) ? $itemData['qty_ordered'] : 1;
-                if (!is_numeric($qty)) {
-                    $warnings[] = __('Qty of product contains invalid value, falling back to qty 1.');
-                    $qty = 1;
-                }
-                // Get customer group
                 $customer = $this->registry->registry('xtento_orderimport_current_customer');
                 if ($customer !== null && $customer->getId()) {
                     $customerGroupId = $customer->getGroupId();
