Index: vendor/magenerds/baseprice/view/frontend/templates/configurable/afterprice.phtml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/magenerds/baseprice/view/frontend/templates/configurable/afterprice.phtml b/vendor/magenerds/baseprice/view/frontend/templates/configurable/afterprice.phtml
--- a/vendor/magenerds/baseprice/view/frontend/templates/configurable/afterprice.phtml
+++ b/vendor/magenerds/baseprice/view/frontend/templates/configurable/afterprice.phtml	(date 1737193909953)
@@ -38,7 +38,9 @@
                     result = swatchWidget.options.jsonConfig.optionPrices[_.findKey(swatchWidget.options.jsonConfig.index, options)];
                 } else {
                     var configurableWidget = $('#product_addtocart_form').data('mage-configurable');
-
+                    if(typeof configurableWidget == "undefined"){
+                        return;
+                    }
                     var options = {};

                     configurableWidget.element.find(configurableWidget.options.superSelector + ' option:selected').each(function () {
