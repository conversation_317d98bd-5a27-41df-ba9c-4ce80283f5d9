Index: vendor/amasty/mostviewed/Block/Widget/Related.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/amasty/mostviewed/Block/Widget/Related.php b/vendor/amasty/mostviewed/Block/Widget/Related.php
--- a/vendor/amasty/mostviewed/Block/Widget/Related.php
+++ b/vendor/amasty/mostviewed/Block/Widget/Related.php	(date 1712665407408)
@@ -179,7 +179,7 @@
         }

         if (!$group && $entityId) {
-            $group = $this->groupRepository->getGroupByIdAndPosition($entityId, $this->getPosition(), $shift);
+            $group = $this->groupRepository->getGroupByIdAndPosition($entityId, $this->getPosition()??"", $shift);
         }

         return $group;
