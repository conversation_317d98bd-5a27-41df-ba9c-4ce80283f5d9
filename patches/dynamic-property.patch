Index: vendor/creativestyle/magesuite-email-attachments/Mail/Template/TransportBuilder.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/creativestyle/magesuite-email-attachments/Mail/Template/TransportBuilder.php b/vendor/creativestyle/magesuite-email-attachments/Mail/Template/TransportBuilder.php
--- a/vendor/creativestyle/magesuite-email-attachments/Mail/Template/TransportBuilder.php
+++ b/vendor/creativestyle/magesuite-email-attachments/Mail/Template/TransportBuilder.php	(date 1737649693752)
@@ -33,6 +33,7 @@
  * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
  * @since 100.0.2
  */
+#[\AllowDynamicProperties]
 class TransportBuilder extends \Magento\Framework\Mail\Template\TransportBuilder
 {
     /**
