--- a/vendor/xtento/orderexport/Block/Adminhtml/Profile/Edit/Tab/Filters.php
+++ b/vendor/xtento/orderexport/Block/Adminhtml/Profile/Edit/Tab/Filters.php
@@ -131,6 +131,21 @@
             ['legend' => __('%1 Filters', ucwords($model->getEntity())), 'class' => 'fieldset-wide']
         );
 
+        if ($entity === Export::ENTITY_SHIPMENT) {
+            $fieldset->addField(
+                'export_filter_bundle_without_child_products',
+                'select',
+                [
+                    'label' => __('Export bundle products without children', $entityName),
+                    'name' => 'export_filter_bundle_without_child_products',
+                    'values' => $this->yesNo->toOptionArray(),
+                    'note' => __(
+                        'This option is for exporting bundle products with cumulated costs of it\'s child products. Child products (simple products) won\'t be exported when this option is set to \'yes\'.'
+                    )
+                ]
+            );
+        }
+
         $fieldset->addField(
             'export_filter_new_only',
             'select',
