Index: vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php b/vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php
--- a/vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php
+++ b/vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php	(date 1737112946254)
@@ -32,7 +32,7 @@
         $this->reset()
             ->setUnitPrice($fptTax)
             ->setTotalAmount($fptTax)
-            ->setTitle($fpt['name'])
-            ->setReference($fpt['reference']);
+            ->setTitle($fpt['name'][0])
+            ->setReference($fpt['reference'][0]);
     }
 }
