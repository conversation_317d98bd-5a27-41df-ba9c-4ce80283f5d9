Index: vendor/magento/module-offline-shipping/Model/Carrier/Tablerate.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/magento/module-offline-shipping/Model/Carrier/Tablerate.php b/vendor/magento/module-offline-shipping/Model/Carrier/Tablerate.php
--- a/vendor/magento/module-offline-shipping/Model/Carrier/Tablerate.php
+++ b/vendor/magento/module-offline-shipping/Model/Carrier/Tablerate.php	(date 1742813064439)
@@ -133,7 +133,8 @@
                     foreach ($item->getChildren() as $child) {
                         if ($child->getFreeShipping() && !$child->getProduct()->isVirtual()) {
                             $freeShipping = (int)$child->getFreeShipping();
-                            $freeQty += $item->getQty() * ($child->getQty() - $freeShipping);
+//                            $freeQty += $item->getQty() * ($child->getQty() - $freeShipping);
+                              $freeQty += $child->getQty();
                         }
                     }
                 } elseif (($item->getFreeShipping() || $item->getAddress()->getFreeShipping()) &&
