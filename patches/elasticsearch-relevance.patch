From 08a372e786236c2860328052086372bdda6f267b Mon Sep 17 00:00:00 2001
From: Vadym Honcharuk <<EMAIL>>
Date: Wed, 3 Aug 2022 17:18:42 +0300
Subject: [PATCH] Magento 2.4.5 (#2663): Fixing call to a member function
 getEuCountryList() on null

---
 ...ile_elasticsuite_search_request_relevanceconfig_edit.xml | 6 +++++-
 1 file changed, 5 insertions(+), 1 deletion(-)

diff --git a/src/module-elasticsuite-core/view/adminhtml/layout/smile_elasticsuite_search_request_relevanceconfig_edit.xml b/src/module-elasticsuite-core/view/adminhtml/layout/smile_elasticsuite_search_request_relevanceconfig_edit.xml
index cb06015fe..73cffdc64 100644
--- a/src/module-elasticsuite-core/view/adminhtml/layout/smile_elasticsuite_search_request_relevanceconfig_edit.xml
+++ b/src/module-elasticsuite-core/view/adminhtml/layout/smile_elasticsuite_search_request_relevanceconfig_edit.xml
@@ -18,7 +18,11 @@
 <page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
     <body>
         <referenceContainer name="js">
-            <block class="Magento\Backend\Block\Template" template="Magento_Config::system/config/js.phtml"/>
+            <block class="Magento\Backend\Block\Template" name="js.system_config_js" template="Magento_Config::system/config/js.phtml">
+                <arguments>
+                    <argument name="countryFilter" xsi:type="object">Magento\Config\ViewModel\CountryFilter</argument>
+                </arguments>
+            </block>
         </referenceContainer>
         <referenceContainer name="page.main.actions">
             <block class="Smile\ElasticsuiteCore\Block\Adminhtml\Search\Request\RelevanceConfig\Scope\Switcher" name="smile.elasticsuite.relevance.config.switcher" template="Smile_ElasticsuiteCore::relevance/configuration/scope/switcher.phtml">