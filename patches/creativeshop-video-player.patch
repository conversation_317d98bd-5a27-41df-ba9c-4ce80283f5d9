--- a/vendor/creativestyle/theme-creativeshop/src/components/video-player/video-player.ts
+++ b/vendor/creativestyle/theme-creativeshop/src/components/video-player/video-player.ts
@@ -71,6 +71,7 @@
     public _ytModal: any;
     public _ytPlayer: any;
     protected _$videosTriggers: JQuery;
+    public _ytApiLoaded: any;
 
     /**
      * Creates new VideoPlayer component with optional settings.
@@ -90,10 +91,7 @@
         );
 
         if (this._$videosTriggers.length) {
-            if (!this._isYTapiLoaded()) {
-                this._loadYTapi();
-            }
-            this.renderVideoPlayer();
+            this._setVideoEvents();
 
             if (this._options.useModal) {
                 this.renderModal();
@@ -159,26 +157,21 @@
     /**
      * Renders Iframe with YT video player as soon as Youtube's Iframe API is loaded
      */
-    public renderVideoPlayer(): any {
-        const _obj: any = this;
-        const onYTplayerReady: any = this._setVideoEvents.bind(this);
-
-        function onYouTubeIframeAPIReady(): void {
-            _obj._ytPlayer = new YT.Player(_obj._options.videoPlayerId, {
-                width: _obj._options.videoPlayerWidth,
-                height: _obj._options.videoPlayerHeight,
-                playerVars: {
-                    autoplay: _obj._options.videoAutoplay,
-                    controls: 1,
-                    rel: 0,
-                },
-                events: {
-                    onReady: onYTplayerReady,
-                },
-            });
-        }
+    public renderVideoPlayer(onYTplayerReady): any {
+        onYTplayerReady = typeof onYTplayerReady !== 'undefined' ? onYTplayerReady : () => {};
 
-        window.onYouTubeIframeAPIReady = onYouTubeIframeAPIReady.bind(this);
+        this._ytPlayer = new YT.Player(this._options.videoPlayerId, {
+            width: this._options.videoPlayerWidth,
+            height: this._options.videoPlayerHeight,
+            playerVars: {
+                autoplay: this._options.videoAutoplay,
+                controls: 1,
+                rel: 0,
+            },
+            events: {
+                onReady: onYTplayerReady,
+            },
+        });
     }
 
     /**
@@ -223,10 +216,7 @@
      * Checks if API script has been already added to the DOM
      */
     protected _isYTapiLoaded(): boolean {
-        return (
-            $('head').find('script[src*="https://www.youtube.com/iframe_api"]')
-                .length > 0
-        );
+        return true === this._ytApiLoaded && $('head').find('script[src*="https://www.youtube.com/iframe_api"]').length > 0;
     }
 
     /**
@@ -234,6 +224,10 @@
      * onYouTubeIframeAPIReady() has to be accessible globaly
      */
     protected _loadYTapi(): void {
+        if ($('head').find('script[src*="https://www.youtube.com/iframe_api"]').length > 0) {
+            return;
+        }
+
         const tag: any = document.createElement('script');
         tag.src = 'https://www.youtube.com/iframe_api';
         const firstScriptTag: any = document.getElementsByTagName('script')[0];
@@ -244,14 +238,29 @@
      * Loads video with given ID into player and opens modal
      */
     protected _runYTvideo(videoId: string): void {
-        if (this._options.videoAutoplay) {
-            this._ytPlayer.loadVideoById(videoId);
-        } else {
-            this._ytPlayer.cueVideoById(videoId);
-        }
+        const _obj: any = this;
 
-        if (this._options.useModal) {
-            this.openModal();
+        const loadVideo = () => {
+            if (_obj._options.videoAutoplay) {
+                _obj._ytPlayer.loadVideoById(videoId);
+            } else {
+                _obj._ytPlayer.cueVideoById(videoId);
+            }
+
+            if (_obj._options.useModal) {
+                _obj.openModal();
+            }
+        };
+
+        if (!this._isYTapiLoaded()) {
+            window.onYouTubeIframeAPIReady = () => {
+                _obj._ytApiLoaded = true;
+                _obj.renderVideoPlayer(loadVideo);
+            }
+
+            this._loadYTapi();
+        } else {
+            loadVideo();
         }
     }
 }
