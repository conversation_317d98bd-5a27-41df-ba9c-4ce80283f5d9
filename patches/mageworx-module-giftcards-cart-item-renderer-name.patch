--- a/vendor/mageworx/module-giftcardsbase/view/frontend/layout/checkout_cart_item_renderers.xml
+++ b/vendor/mageworx/module-giftcardsbase/view/frontend/layout/checkout_cart_item_renderers.xml
@@ -9,7 +9,7 @@
     <update handle="checkout_item_price_renderers"/>
     <body>
         <referenceBlock name="checkout.cart.item.renderers">
-            <block class="MageWorx\GiftCards\Block\Checkout\Cart\Item\Renderer" as="mageworx_giftcards" template="Magento_Checkout::cart/item/default.phtml">
+            <block class="MageWorx\GiftCards\Block\Checkout\Cart\Item\Renderer" name="checkout.cart.item.renderers.mageworx_giftcards" as="mageworx_giftcards" template="Magento_Checkout::cart/item/default.phtml">
                 <block class="Magento\Checkout\Block\Cart\Item\Renderer\Actions" name="checkout.cart.item.renderers.mageworx_giftcards.actions" as="actions">
                     <block class="Magento\Checkout\Block\Cart\Item\Renderer\Actions\Edit" name="checkout.cart.item.renderers.mageworx_giftcards.actions.edit" template="Magento_Checkout::cart/item/renderer/actions/edit.phtml"/>
                     <block class="Magento\Checkout\Block\Cart\Item\Renderer\Actions\Remove" name="checkout.cart.item.renderers.mageworx_giftcards.actions.remove" template="Magento_Checkout::cart/item/renderer/actions/remove.phtml"/>
