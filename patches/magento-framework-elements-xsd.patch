--- a/vendor/magento/framework/View/Layout/etc/elements.xsd
+++ b/vendor/magento/framework/View/Layout/etc/elements.xsd
@@ -328,6 +328,7 @@
         <xs:attribute type="elementNameType" name="name" use="required"/>
         <xs:attribute type="xs:string" name="template" use="optional"/>
         <xs:attribute type="xs:string" name="class" use="optional"/>
+        <xs:attribute type="xs:string" name="group" use="optional"/>
         <xs:attribute type="xs:boolean" name="display" default="true" use="optional"/>
         <xs:attribute type="xs:boolean" name="remove" use="optional"/>
     </xs:complexType>
