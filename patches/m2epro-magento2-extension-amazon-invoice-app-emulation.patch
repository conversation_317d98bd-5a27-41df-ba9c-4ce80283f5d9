diff --git a/vendor/m2epro/magento2-extension/Block/Adminhtml/Magento/Payment/Info.php b/vendor/m2epro/magento2-extension/Block/Adminhtml/Magento/Payment/Info.php
--- a/vendor/m2epro/magento2-extension/Block/Adminhtml/Magento/Payment/Info.php
+++ b/vendor/m2epro/magento2-extension/Block/Adminhtml/Magento/Payment/Info.php	(date 1666160607000)
@@ -48,8 +48,10 @@
      */
     protected function _toHtml()
     {
-        $this->setData('area', \Magento\Framework\App\Area::AREA_ADMINHTML);
-        return parent::_toHtml();
+        $this->appEmulation->startEnvironmentEmulation(\Magento\Framework\App\Area::AREA_ADMINHTML, true);
+        $result = parent::_toHtml();
+        $this->appEmulation->stopEnvironmentEmulation();
+        return $result;
     }

     //########################################
