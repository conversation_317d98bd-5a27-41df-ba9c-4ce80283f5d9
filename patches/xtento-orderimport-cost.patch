--- a/vendor/xtento/orderimport/Model/Import/Processor/Order/Item.php
+++ b/vendor/xtento/orderimport/Model/Import/Processor/Order/Item.php
@@ -398,6 +398,9 @@
                     $item->setProductType('simple');
                 }
             }
+            if (!isset($itemData['base_cost']) && !$item->getBaseCost()) {
+                $item->setBaseCost((float)$product->getCost());
+            }
             if (!isset($itemData['base_price_incl_tax']) && !$item->getBasePriceInclTax()) {
                 $item->setBasePriceInclTax((float)$item->getBasePrice() + (float)$item->getBaseTaxAmount());
             }
