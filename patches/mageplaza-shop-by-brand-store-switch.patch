--- a/vendor/mageplaza/magento-2-shop-by-brand/Controller/Adminhtml/Attribute/Update.php
+++ b/vendor/mageplaza/magento-2-shop-by-brand/Controller/Adminhtml/Attribute/Update.php
@@ -107,7 +107,7 @@
      */
     public function execute()
     {
-        $optionStore    = 0;
+        $optionStore    = (int)$this->getRequest()->getParam('store', 0);
         $check          = 0;
         $attributeCodes = [];
         $result         = ['success' => false];
@@ -128,7 +128,6 @@
             foreach ($options as $option) {
                 if ((float)$option->getValue() === (float)$optionId) {
                     $result      = ['success' => true];
-                    $optionStore = $key;
                     $check       = 1;
                     break;
                 }
