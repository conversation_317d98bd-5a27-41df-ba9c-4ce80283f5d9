--- a/vendor/magento/framework/Filter/DirectiveProcessor/SimpleDirective.php
+++ b/vendor/magento/framework/Filter/DirectiveProcessor/SimpleDirective.php
@@ -63,6 +63,11 @@
      */
     public function process(array $construction, Template $filter, array $templateVariables): string
     {
+        // Incompatibility with fooman extensions (https://gitlab.com/inspiredminds/myproduct/-/issues/602)
+        if (empty($construction)) {
+            return '';
+        }
+
         try {
             $directiveParser = $this->processorPool
                 ->get($construction['directiveName']);
