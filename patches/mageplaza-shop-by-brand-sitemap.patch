--- a/vendor/mageplaza/magento-2-shop-by-brand/Model/Sitemap.php
+++ b/vendor/mageplaza/magento-2-shop-by-brand/Model/Sitemap.php
@@ -146,7 +146,7 @@
                         'caption' => null,
                     ]
                 );
-                $images = new DataObject(['collection' => $imagesCollection]);
+                $images = new DataObject(['collection' => $imagesCollection, 'title' => $this->helper->getBrandTitle()]);
             }
 
             $itemId = $item->getId();
