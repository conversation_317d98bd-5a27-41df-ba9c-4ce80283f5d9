--- a/vendor/amasty/module-gift-card/view/frontend/layout/checkout_cart_item_renderers.xml
+++ b/vendor/amasty/module-gift-card/view/frontend/layout/checkout_cart_item_renderers.xml
@@ -10,7 +10,7 @@
 <page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
         <referenceBlock name="checkout.cart.item.renderers">
-            <block class="Amasty\GiftCard\Block\Checkout\Cart\Item\Renderer" as="amgiftcard" template="Magento_Checkout::cart/item/default.phtml">
+            <block class="Amasty\GiftCard\Block\Checkout\Cart\Item\Renderer" name="checkout.cart.item.renderers.amgiftcard" as="amgiftcard" template="Magento_Checkout::cart/item/default.phtml">
                 <block class="Magento\Checkout\Block\Cart\Item\Renderer\Actions" name="checkout.cart.item.renderers.amgiftcard.actions" as="actions">
                     <block class="Magento\Checkout\Block\Cart\Item\Renderer\Actions\Edit" name="checkout.cart.item.renderers.amgiftcard.actions.edit" template="Magento_Checkout::cart/item/renderer/actions/edit.phtml"/>
                     <block class="Magento\Checkout\Block\Cart\Item\Renderer\Actions\Remove" name="checkout.cart.item.renderers.amgiftcard.actions.remove" template="Magento_Checkout::cart/item/renderer/actions/remove.phtml"/>
