--- a/vendor/fooman/pdfcustomiser-implementation-m2/src/Block/TaxTable.php
+++ b/vendor/fooman/pdfcustomiser-implementation-m2/src/Block/TaxTable.php
@@ -59,7 +59,7 @@
             $taxItems = $this->getTaxItems($orderItem);
             if (!empty($taxItems)) {
                 foreach ($taxItems as $taxItem) {
-                    $this->aggregateTaxFromTaxItem($taxItem->getTaxPercent(), $taxItem, $orderItem);
+                    $this->aggregateTaxFromTaxItem($taxItem->getTaxPercent(), $taxItem, $item);
                 }
             }
         }
@@ -158,14 +158,14 @@
         }
     }
 
-    private function aggregateTaxFromTaxItem($rate, $item, $orderItem)
+    private function aggregateTaxFromTaxItem($rate, $taxIt, $item)
     {
         if (isset($this->accumulatedTaxes[$rate])) {
-            $this->accumulatedTaxes[$rate]['base_tax_basis'] += $this->sumTaxBasis($orderItem);
-            $this->accumulatedTaxes[$rate]['base_tax_amount'] += $item->getBaseAmount() - $item->getRealBaseAmount();
+            $this->accumulatedTaxes[$rate]['base_tax_basis'] += $this->sumTaxBasis($item);
+            $this->accumulatedTaxes[$rate]['base_tax_amount'] += $taxIt->getBaseAmount() - $taxIt->getRealBaseAmount();
         } else {
-            $this->accumulatedTaxes[$rate]['base_tax_basis'] = $this->sumTaxBasis($orderItem);
-            $this->accumulatedTaxes[$rate]['base_tax_amount'] = $item->getBaseAmount() - $item->getRealBaseAmount();
+            $this->accumulatedTaxes[$rate]['base_tax_basis'] = $this->sumTaxBasis($item);
+            $this->accumulatedTaxes[$rate]['base_tax_amount'] = $taxIt->getBaseAmount() - $taxIt->getRealBaseAmount();
         }
     }
 
