Index: vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Rule.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Rule.php b/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Rule.php
--- a/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Rule.php
+++ b/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Rule.php	(date 1739004015383)
@@ -485,15 +485,31 @@
      */
     private function addChildrenQueries($query, CategoryInterface $category, $excludedCategories = []): QueryInterface
     {
+        $isStandardCategory = ! $category->getIsVirtualCategory();
         $childrenCategories    = $this->getChildrenCategories($category, $excludedCategories);
         $childrenCategoriesIds = [];

         if ($query !== null && $childrenCategories->getSize() > 0) {
             $queryParams = ['should' => [$query], 'cached' => empty($excludedCategories)];

+            $childrenCategoriesIds = [];
+            foreach ($childrenCategories as $childrenCategory) {
+                $childrenCategoriesIds[] = $childrenCategory->getId();
+            }
+
             foreach ($childrenCategories as $childrenCategory) {
-                if (((bool) $childrenCategory->getIsVirtualCategory()) === true) {
-                    $childrenQuery = $this->getCategorySearchQuery($childrenCategory, $excludedCategories);
+                $isChildrenVirtual = (bool) $childrenCategory->getIsVirtualCategory();
+                if ($isChildrenVirtual) {
+                    $childrenQuery = null;
+                    $virtualRootCategory = $childrenCategory->getVirtualCategoryRoot();
+                    if (!$virtualRootCategory
+                        || (
+                            !in_array($virtualRootCategory, $childrenCategoriesIds)
+                            && $virtualRootCategory != $category->getId()
+                        )
+                    ) {
+                        $childrenQuery = $this->getCategorySearchQuery($childrenCategory, $excludedCategories);
+                    }
                     if ($childrenQuery !== null) {
                         $childrenQuery->setName(
                             sprintf(
@@ -505,13 +521,13 @@
                         );
                         $queryParams['should'][] = $childrenQuery;
                     }
-                } else {
-                    $childrenCategoriesIds[] = $childrenCategory->getId();
+                } elseif (!$isStandardCategory) {
+                    $childrenStandardCategoriesIds[] = $childrenCategory->getId();
                 }
             }

-            if (!empty($childrenCategoriesIds)) {
-                $standardChildrenQuery = $this->getStandardCategoriesQuery($childrenCategoriesIds, $excludedCategories);
+            if (!empty($childrenStandardCategoriesIds)) {
+                $standardChildrenQuery = $this->getStandardCategoriesQuery($childrenStandardCategoriesIds, $excludedCategories);
                 $standardChildrenQuery->setName(
                     sprintf(
                         '(%s) standard children of virtual category [%s]:%d',
@@ -555,10 +571,6 @@

         $categoryCollection->addIsActiveFilter()->addPathFilter(sprintf('%s/.*', $category->getPath()));

-        if (((bool) $category->getIsVirtualCategory()) === false) {
-            $categoryCollection->addFieldToFilter('is_virtual_category', '1');
-        }
-
         if (!empty($excludedCategories)) {
             $categoryCollection->addAttributeToFilter('entity_id', ['nin' => $excludedCategories]);
         }
