Index: vendor/magento/module-weee/Model/Total/Invoice/Weee.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/magento/module-weee/Model/Total/Invoice/Weee.php b/vendor/magento/module-weee/Model/Total/Invoice/Weee.php
--- a/vendor/magento/module-weee/Model/Total/Invoice/Weee.php
+++ b/vendor/magento/module-weee/Model/Total/Invoice/Weee.php	(date 1741689357173)
@@ -206,11 +206,12 @@

             $invoice->setSubtotal($invoice->getSubtotal() + $totalWeeeAmount);
             $invoice->setBaseSubtotal($invoice->getBaseSubtotal() + $baseTotalWeeeAmount);
-        }
-
             // need to add the Weee amounts including all their taxes
             $invoice->setSubtotalInclTax($invoice->getSubtotalInclTax() + $totalWeeeAmountInclTax);
             $invoice->setBaseSubtotalInclTax($invoice->getBaseSubtotalInclTax() + $baseTotalWeeeAmountInclTax);
+        }
+
+

         $invoice->setGrandTotal($invoice->getGrandTotal() + $totalWeeeAmount + $totalWeeeTaxAmount);
         $invoice->setBaseGrandTotal($invoice->getBaseGrandTotal() + $baseTotalWeeeAmount + $baseTotalWeeeTaxAmount);
