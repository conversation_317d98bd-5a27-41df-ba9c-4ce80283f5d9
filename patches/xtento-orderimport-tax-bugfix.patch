--- a/vendor/xtento/orderimport/Model/Import/Processor/Order/Item.php
+++ b/vendor/xtento/orderimport/Model/Import/Processor/Order/Item.php
@@ -18,6 +18,11 @@
 class Item extends \Xtento\OrderImport\Model\Import\Processor\AbstractProcessor
 {
     /**
+     * @var \Magento\Framework\App\Config\ScopeConfigInterface
+     */
+    protected $scopeConfig;
+
+    /**
      * @var Registry
      */
     protected $registry;
@@ -65,6 +70,7 @@
     /**
      * Item constructor.
      *
+     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
      * @param Registry $registry
      * @param \Magento\Tax\Model\Calculation $taxCalculation
      * @param \Magento\Catalog\Model\ProductFactory $productFactory
@@ -76,6 +82,7 @@
      * @param array $data
      */
     public function __construct(
+        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
         Registry $registry,
         \Magento\Tax\Model\Calculation $taxCalculation,
         \Magento\Catalog\Model\ProductFactory $productFactory,
@@ -88,6 +95,7 @@
         array $data = []
     ) {
         parent::__construct($data);
+        $this->scopeConfig = $scopeConfig;
         $this->registry = $registry;
         $this->taxCalculation = $taxCalculation;
         $this->productFactory = $productFactory;
@@ -526,15 +534,14 @@
         $priceExclTax = (float)$price;
         $taxPercent = $this->getTaxPercent($order, $product);
         if ($taxPercent > 0) {
-            //if (!Mage::getStoreConfig(Mage_Tax_Model_Config::CONFIG_XML_PATH_PRICE_INCLUDES_TAX, $product->getStore())) {
-            // Prices are excluding tax -> add tax
-            $priceExclTax = $price;
-            $priceInclTax = $priceExclTax * (1 + $taxPercent / 100);
-            /*} else {
+            if ($this->scopeConfig->getValue(\Magento\Tax\Model\Config::CONFIG_XML_PATH_PRICE_INCLUDES_TAX) !== '1') {
+                // Prices are excluding tax -> add tax
+                $priceInclTax = $priceExclTax * (1 + $taxPercent / 100);
+            }
+            else {
                 // Prices are including tax - do not add tax to price
-                $priceInclTax = $price;
                 $priceExclTax = $priceInclTax / (1 + $taxPercent / 100);
-            }*/
+            }
         }
         if (!isset($itemData['base_price']) || $itemData['base_price'] == $priceInclTax) {
             $itemData['base_price'] = $priceExclTax;
