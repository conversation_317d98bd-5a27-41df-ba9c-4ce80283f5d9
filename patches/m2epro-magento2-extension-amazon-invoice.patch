--- a/vendor/m2epro/magento2-extension/Block/Adminhtml/Magento/Payment/Info.php
+++ b/vendor/m2epro/magento2-extension/Block/Adminhtml/Magento/Payment/Info.php
@@ -49,6 +49,9 @@
     protected function _toHtml()
     {
         $this->setData('area', \Magento\Framework\App\Area::AREA_ADMINHTML);
+        // 1. enter absolute path to theme you need
+        // 2. current or releases/71 - try anything
+         $this->setData('theme_dir', '/home/<USER>/www.myproduct.at/current/app/design/frontend/creativestyle/theme-myproduct');
         return parent::_toHtml();
     }
 
