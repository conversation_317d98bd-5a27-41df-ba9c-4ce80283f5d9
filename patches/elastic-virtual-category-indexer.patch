Index: vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php b/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php
--- a/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php
+++ b/vendor/smile/elasticsuite/src/module-elasticsuite-virtual-category/Model/Preview.php	(date 1716296180940)
@@ -212,7 +212,7 @@
      *
      * @return string
      */
-    private function getSortBy() : string
+    private function getSortBy()
     {
         if (!$this->sortBy) {
             $useConfig = $this->request->getParam('use_config', []);
