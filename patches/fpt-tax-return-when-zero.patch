Index: vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php b/vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php
--- a/vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php	
+++ b/vendor/klarna/module-orderlines/Model/Items/Surcharge/Calculator.php	(date 1737626346079)
@@ -28,7 +28,9 @@
         $this->reset();
         $fpt = $dataHolder->getFptTax();
         $fptTax = $this->dataConverter->toApiFloat($fpt['tax']);
-
+        if($fptTax == 0){
+            return;
+        }
         $this->reset()
             ->setUnitPrice($fptTax)
             ->setTotalAmount($fptTax)
