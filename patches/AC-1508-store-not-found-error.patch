Index: vendor/magento/module-sales/Ui/Component/Listing/Column/Price.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/magento/module-sales/Ui/Component/Listing/Column/Price.php b/vendor/magento/module-sales/Ui/Component/Listing/Column/Price.php
--- a/vendor/magento/module-sales/Ui/Component/Listing/Column/Price.php
+++ b/vendor/magento/module-sales/Ui/Component/Listing/Column/Price.php	(date 1719395045132)
@@ -8,6 +8,7 @@
 namespace Magento\Sales\Ui\Component\Listing\Column;

 use Magento\Framework\App\ObjectManager;
+use Magento\Framework\Exception\NoSuchEntityException;
 use Magento\Framework\View\Element\UiComponent\ContextInterface;
 use Magento\Framework\View\Element\UiComponentFactory;
 use Magento\Store\Model\Store;
@@ -82,7 +83,11 @@
                     $itemStoreId = $item['store_id'] ?? '';
                     $storeId = $itemStoreId && is_numeric($itemStoreId) ? $itemStoreId :
                         $this->context->getFilterParam('store_id', Store::DEFAULT_STORE_ID);
-                    $store = $this->storeManager->getStore($storeId);
+                    try {
+                        $store = $this->storeManager->getStore($storeId);
+                    } catch (NoSuchEntityException $entityException){
+                        $store = $this->storeManager->getStore(Store::DEFAULT_STORE_ID);
+                    }
                     $currencyCode = $store->getBaseCurrency()->getCurrencyCode();
                 }
                 $basePurchaseCurrency = $this->currency->load($currencyCode);
