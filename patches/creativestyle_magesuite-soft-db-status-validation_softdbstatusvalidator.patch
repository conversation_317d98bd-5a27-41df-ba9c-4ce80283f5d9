--- a/vendor/creativestyle/magesuite-soft-db-status-validation/Module/Plugin/SoftDbStatusValidator.php
+++ b/vendor/creativestyle/magesuite-soft-db-status-validation/Module/Plugin/SoftDbStatusValidator.php
@@ -25,11 +25,12 @@
     public function __construct(
         \Magento\Framework\Cache\FrontendInterface $cache,
         \Magento\Framework\Module\DbVersionInfo $dbVersionInfo,
+        \Magento\Framework\App\DeploymentConfig $deploymentConfig,
         \MageSuite\SoftDbStatusValidation\Model\Config $config,
         \Magento\Framework\App\State $appState,
         \Psr\Log\LoggerInterface $logger
     ) {
-        parent::__construct($cache, $dbVersionInfo);
+        parent::__construct($cache, $dbVersionInfo, $deploymentConfig);
 
         $this->config = $config;
         $this->logger = $logger;
